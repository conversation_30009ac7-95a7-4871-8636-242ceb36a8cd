<?php
/**
 * Development Server Launcher
 * Quick way to start the application for testing
 */

// Check PHP version
if (version_compare(PHP_VERSION, '7.4.0', '<')) {
    echo "Error: PHP 7.4.0 or higher is required. Current version: " . PHP_VERSION . "\n";
    exit(1);
}

// Check required extensions
$requiredExtensions = ['mbstring', 'json', 'session'];
$missingExtensions = [];

foreach ($requiredExtensions as $ext) {
    if (!extension_loaded($ext)) {
        $missingExtensions[] = $ext;
    }
}

if (!empty($missingExtensions)) {
    echo "Error: Missing required PHP extensions: " . implode(', ', $missingExtensions) . "\n";
    exit(1);
}

// Create necessary directories
$directories = ['cache', 'logs'];
foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
        echo "Created directory: $dir\n";
    }
}

// Set permissions
foreach ($directories as $dir) {
    if (!is_writable($dir)) {
        chmod($dir, 0755);
        echo "Set permissions for: $dir\n";
    }
}

// Configuration
$host = '127.0.0.1';
$port = 8000;
$docroot = __DIR__;

// Check if port is available
$socket = @fsockopen($host, $port, $errno, $errstr, 1);
if ($socket) {
    fclose($socket);
    echo "Error: Port $port is already in use.\n";
    echo "Try a different port: php serve.php --port=8001\n";
    exit(1);
}

// Parse command line arguments
$options = getopt('', ['port:', 'host:', 'help']);

if (isset($options['help'])) {
    echo "Text Search & Replace Tool - Development Server\n\n";
    echo "Usage: php serve.php [options]\n\n";
    echo "Options:\n";
    echo "  --host=HOST    Server host (default: 127.0.0.1)\n";
    echo "  --port=PORT    Server port (default: 8000)\n";
    echo "  --help         Show this help message\n\n";
    echo "Examples:\n";
    echo "  php serve.php\n";
    echo "  php serve.php --port=8080\n";
    echo "  php serve.php --host=0.0.0.0 --port=8080\n\n";
    exit(0);
}

if (isset($options['port'])) {
    $port = (int)$options['port'];
    if ($port < 1024 || $port > 65535) {
        echo "Error: Port must be between 1024 and 65535\n";
        exit(1);
    }
}

if (isset($options['host'])) {
    $host = $options['host'];
}

// Router for development server
$router = function($uri) {
    // Remove query string
    $uri = parse_url($uri, PHP_URL_PATH);
    
    // API routes
    if (strpos($uri, '/api/') === 0) {
        $_SERVER['REQUEST_URI'] = $uri;
        require_once 'api/index.php';
        return true;
    }
    
    // Static files
    $file = __DIR__ . $uri;
    if (is_file($file)) {
        return false; // Let PHP serve the file
    }
    
    // Default to index.php
    if ($uri === '/' || $uri === '') {
        require_once 'index.php';
        return true;
    }
    
    // 404 for other routes
    http_response_code(404);
    echo "<h1>404 Not Found</h1>";
    echo "<p>The requested URL was not found on this server.</p>";
    echo "<p><a href='/'>← Back to Home</a></p>";
    return true;
};

// Create router file for PHP built-in server
$routerFile = sys_get_temp_dir() . '/text_search_router.php';
file_put_contents($routerFile, '<?php
$uri = $_SERVER["REQUEST_URI"];

// Remove query string
$uri = parse_url($uri, PHP_URL_PATH);

// API routes
if (strpos($uri, "/api/") === 0) {
    $_SERVER["REQUEST_URI"] = $uri;
    require_once __DIR__ . "/api/index.php";
    return true;
}

// Static files
$file = __DIR__ . $uri;
if (is_file($file)) {
    return false; // Let PHP serve the file
}

// Default to index.php
if ($uri === "/" || $uri === "") {
    require_once __DIR__ . "/index.php";
    return true;
}

// 404 for other routes
http_response_code(404);
echo "<h1>404 Not Found</h1>";
echo "<p>The requested URL was not found on this server.</p>";
echo "<p><a href=\"/\">← Back to Home</a></p>";
return true;
');

// Display startup information
echo "\n";
echo "🚀 Text Search & Replace Tool - Development Server\n";
echo "================================================\n\n";
echo "📋 System Information:\n";
echo "   PHP Version: " . PHP_VERSION . "\n";
echo "   Server: PHP Built-in Server\n";
echo "   Document Root: $docroot\n\n";
echo "🌐 Server URLs:\n";
echo "   Main Application: http://$host:$port/\n";
echo "   API Endpoint: http://$host:$port/api/\n";
echo "   Tests: http://$host:$port/tests/run_tests.php\n\n";
echo "📊 API Examples:\n";
echo "   Status: curl http://$host:$port/api/status\n";
echo "   Health: curl http://$host:$port/api/health\n\n";
echo "⚡ Quick Commands:\n";
echo "   Open in browser: ";
if (PHP_OS_FAMILY === 'Windows') {
    echo "start http://$host:$port/\n";
} elseif (PHP_OS_FAMILY === 'Darwin') {
    echo "open http://$host:$port/\n";
} else {
    echo "xdg-open http://$host:$port/\n";
}
echo "   Stop server: Ctrl+C\n\n";
echo "🔧 Development Features:\n";
echo "   ✅ Auto-reload on file changes\n";
echo "   ✅ Error reporting enabled\n";
echo "   ✅ API endpoints available\n";
echo "   ✅ Test suite accessible\n\n";

// Start the server
echo "🎯 Starting server...\n";
echo "   Listening on http://$host:$port/\n";
echo "   Press Ctrl+C to stop\n\n";

// Build the command
$command = sprintf(
    'php -S %s:%d -t %s %s',
    escapeshellarg($host),
    $port,
    escapeshellarg($docroot),
    escapeshellarg($routerFile)
);

// Execute the server
$descriptors = [
    0 => STDIN,
    1 => STDOUT,
    2 => STDERR
];

$process = proc_open($command, $descriptors, $pipes);

if (is_resource($process)) {
    // Handle Ctrl+C gracefully
    if (function_exists('pcntl_signal')) {
        pcntl_signal(SIGINT, function() use ($process, $routerFile) {
            echo "\n\n🛑 Shutting down server...\n";
            proc_terminate($process);
            if (file_exists($routerFile)) {
                unlink($routerFile);
            }
            echo "✅ Server stopped successfully.\n";
            exit(0);
        });
    }
    
    // Wait for the process to finish
    $exitCode = proc_close($process);
    
    // Clean up router file
    if (file_exists($routerFile)) {
        unlink($routerFile);
    }
    
    exit($exitCode);
} else {
    echo "Error: Failed to start server\n";
    exit(1);
}
