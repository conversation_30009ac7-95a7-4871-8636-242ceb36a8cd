# Text Search & Replace Tool - Apache Configuration

# Security Headers
<IfModule mod_headers.c>
    # Prevent XSS attacks
    Head<PERSON> always set X-XSS-Protection "1; mode=block"
    
    # Prevent MIME type sniffing
    Header always set X-Content-Type-Options "nosniff"
    
    # Prevent clickjacking
    Header always set X-Frame-Options "SAMEORIGIN"
    
    # Content Security Policy
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; font-src 'self' https://cdnjs.cloudflare.com; img-src 'self' data:; connect-src 'self';"
    
    # Referrer Policy
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    
    # Remove server signature
    Header always unset Server
    Header always unset X-Powered-By
</IfModule>

# Hide sensitive files and directories
<FilesMatch "^(\.htaccess|\.htpasswd|config\.php|autoload\.php|README\.md|composer\.(json|lock))$">
    Require all denied
</FilesMatch>

# Protect cache and logs directories
<DirectoryMatch "^.*(cache|logs).*$">
    Require all denied
</DirectoryMatch>

# Protect PHP files in classes directory from direct access
<Directory "classes">
    <FilesMatch "\.php$">
        Require all denied
    </FilesMatch>
</Directory>

# Enable compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# Browser caching
<IfModule mod_expires.c>
    ExpiresActive On
    
    # CSS and JavaScript
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    
    # Images
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    
    # Fonts
    ExpiresByType font/woff "access plus 1 month"
    ExpiresByType font/woff2 "access plus 1 month"
    ExpiresByType application/font-woff "access plus 1 month"
    ExpiresByType application/font-woff2 "access plus 1 month"
    
    # HTML
    ExpiresByType text/html "access plus 1 hour"
</IfModule>

# Prevent access to backup files
<FilesMatch "\.(bak|backup|old|tmp|temp|swp|~)$">
    Require all denied
</FilesMatch>

# Limit file upload size (if supported)
<IfModule mod_php.c>
    php_value upload_max_filesize 1M
    php_value post_max_size 2M
    php_value max_execution_time 30
    php_value max_input_time 30
    php_value memory_limit 64M
</IfModule>

# Custom error pages (optional)
# ErrorDocument 404 /error.php?code=404
# ErrorDocument 403 /error.php?code=403
# ErrorDocument 500 /error.php?code=500

# Disable server signature
ServerSignature Off

# Disable directory browsing
Options -Indexes

# Enable URL rewriting (if needed for future features)
<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # Redirect to HTTPS (uncomment in production)
    # RewriteCond %{HTTPS} off
    # RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
    
    # Remove trailing slashes
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} (.+)/$
    RewriteRule ^ %1 [R=301,L]
</IfModule>

# Rate limiting (if mod_evasive is available)
<IfModule mod_evasive24.c>
    DOSHashTableSize    2048
    DOSPageCount        10
    DOSSiteCount        50
    DOSPageInterval     1
    DOSSiteInterval     1
    DOSBlockingPeriod   600
</IfModule>
