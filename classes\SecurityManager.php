<?php
/**
 * SecurityManager Class - Handle security features
 */

class SecurityManager
{
    /**
     * Generate CSRF token
     */
    public static function generateCSRFToken()
    {
        if (!isset($_SESSION[CSRF_TOKEN_NAME])) {
            $_SESSION[CSRF_TOKEN_NAME] = bin2hex(random_bytes(32));
        }
        return $_SESSION[CSRF_TOKEN_NAME];
    }
    
    /**
     * Verify CSRF token
     */
    public static function verifyCSRFToken($token)
    {
        return isset($_SESSION[CSRF_TOKEN_NAME]) && 
               hash_equals($_SESSION[CSRF_TOKEN_NAME], $token);
    }
    
    /**
     * Sanitize input data
     */
    public static function sanitizeInput($input, $allowHTML = false)
    {
        if (is_array($input)) {
            return array_map(function($item) use ($allowHTML) {
                return self::sanitizeInput($item, $allowHTML);
            }, $input);
        }
        
        $input = trim($input);
        
        if (!$allowHTML) {
            $input = htmlspecialchars($input, ENT_QUOTES, 'UTF-8');
        } else {
            // Allow only safe HTML tags
            $allowedTags = '<p><br><strong><em><u><ol><ul><li><h1><h2><h3><h4><h5><h6>';
            $input = strip_tags($input, $allowedTags);
        }
        
        return $input;
    }
    
    /**
     * Validate text length
     */
    public static function validateTextLength($text, $maxLength = MAX_TEXT_LENGTH)
    {
        return mb_strlen($text, 'UTF-8') <= $maxLength;
    }
    
    /**
     * Rate limiting check
     */
    public static function checkRateLimit($identifier, $maxRequests = 100, $timeWindow = 3600)
    {
        $key = 'rate_limit_' . md5($identifier);
        
        if (!isset($_SESSION[$key])) {
            $_SESSION[$key] = [
                'count' => 1,
                'start_time' => time()
            ];
            return true;
        }
        
        $data = $_SESSION[$key];
        
        // Reset if time window has passed
        if (time() - $data['start_time'] > $timeWindow) {
            $_SESSION[$key] = [
                'count' => 1,
                'start_time' => time()
            ];
            return true;
        }
        
        // Check if limit exceeded
        if ($data['count'] >= $maxRequests) {
            return false;
        }
        
        // Increment counter
        $_SESSION[$key]['count']++;
        return true;
    }
    
    /**
     * Log security events
     */
    public static function logSecurityEvent($event, $details = [])
    {
        $logEntry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'event' => $event,
            'details' => $details
        ];
        
        if (LOG_ERRORS) {
            error_log("Security Event: " . json_encode($logEntry), 3, LOG_FILE);
        }
    }
    
    /**
     * Validate regex pattern
     */
    public static function validateRegexPattern($pattern)
    {
        // Basic validation to prevent dangerous patterns
        $dangerousPatterns = [
            '/(?:\(\?\{.*?\})/i', // Code execution
            '/(?:\(\?\#.*?\))/i', // Comments that might hide malicious code
            '/(?:\(\?\<.*?\>)/i', // Named groups with potential issues
        ];
        
        foreach ($dangerousPatterns as $dangerous) {
            if (preg_match($dangerous, $pattern)) {
                return false;
            }
        }
        
        // Test if pattern is valid
        $testResult = @preg_match($pattern, 'test');
        return $testResult !== false;
    }
}
