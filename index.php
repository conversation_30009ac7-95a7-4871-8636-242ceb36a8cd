<?php
// Load autoloader and configuration
require_once 'autoload.php';

// Initialize security
$csrfToken = SecurityManager::generateCSRFToken();

// Process form submission
$processor = new TextProcessor();
$results = null;
$errors = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Verify CSRF token
        if (!SecurityManager::verifyCSRFToken($_POST['csrf_token'] ?? '')) {
            throw new Exception('Invalid security token. Please refresh the page.');
        }

        // Rate limiting
        if (!SecurityManager::checkRateLimit($_SERVER['REMOTE_ADDR'] ?? 'unknown')) {
            throw new Exception('Too many requests. Please try again later.');
        }

        // Sanitize and validate inputs
        $article = SecurityManager::sanitizeInput($_POST['article'] ?? '');
        $search = SecurityManager::sanitizeInput($_POST['search'] ?? '');
        $replace = SecurityManager::sanitizeInput($_POST['replace'] ?? '');
        $caseSensitive = isset($_POST['case_sensitive']);
        $useRegex = isset($_POST['use_regex']);

        // Validate inputs
        if (empty($article)) {
            $errors[] = 'Text content is required.';
        } elseif (!SecurityManager::validateTextLength($article)) {
            $errors[] = 'Text is too long. Maximum length is ' . number_format(MAX_TEXT_LENGTH) . ' characters.';
        }

        if (empty($search)) {
            $errors[] = 'Search term is required.';
        }

        if ($useRegex && !SecurityManager::validateRegexPattern($search)) {
            $errors[] = 'Invalid regular expression pattern.';
        }

        if (empty($errors)) {
            // Configure processor
            $processor->setText($article)
                     ->setSearchTerm($search)
                     ->setReplaceTerm($replace);

            // Check cache first
            $cacheKey = [
                'article' => $article,
                'search' => $search,
                'replace' => $replace,
                'case_sensitive' => $caseSensitive,
                'use_regex' => $useRegex
            ];

            $results = CacheManager::get($cacheKey);

            if ($results === null) {
                // Process text
                $results = [
                    'statistics' => $processor->getStatistics(),
                    'occurrences' => $processor->countOccurrences(),
                    'percentage' => $processor->getPercentage(),
                    'highlighted_text' => $processor->highlightText(),
                    'replaced_text' => !empty($replace) ? $processor->replaceText() : null,
                    'positions' => $processor->findPositions()
                ];

                // Cache results
                CacheManager::set($cacheKey, $results);
            }
        }

    } catch (Exception $e) {
        $errors[] = $e->getMessage();
        SecurityManager::logSecurityEvent('form_processing_error', ['error' => $e->getMessage()]);
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="أداة متقدمة للبحث والاستبدال في النصوص مع إحصائيات مفصلة ودعم التعبيرات النمطية">
    <meta name="keywords" content="بحث, استبدال, نص, أداة, تحليل, إحصائيات">
    <meta name="author" content="<?php echo APP_AUTHOR; ?>">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">

    <title><?php echo APP_NAME; ?> - أداة البحث والاستبدال المتقدمة</title>
</head>
<body>
    <div class="main-container">
        <!-- Header -->
        <div class="header">
            <h1><i class="fas fa-search me-2"></i><?php echo APP_NAME; ?></h1>
            <p>أداة متقدمة للبحث والاستبدال في النصوص مع إحصائيات مفصلة</p>
        </div>

        <!-- Content -->
        <div class="content">
            <!-- Display Errors -->
            <?php if (!empty($errors)): ?>
                <?php foreach ($errors as $error): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>

            <!-- Display any system errors -->
            <?php ErrorHandler::displayErrors(); ?>

            <!-- Main Form -->
            <div class="form-section fade-in">
                <form id="searchForm" method="POST" action="" enctype="multipart/form-data">
                    <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">

                    <!-- Search and Replace Inputs -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="search" class="form-label">
                                <i class="fas fa-search me-1"></i>كلمة البحث *
                            </label>
                            <input type="text"
                                   id="search"
                                   name="search"
                                   class="form-control"
                                   placeholder="أدخل الكلمة أو العبارة للبحث عنها..."
                                   value="<?php echo htmlspecialchars($_POST['search'] ?? ''); ?>"
                                   required
                                   data-bs-toggle="tooltip"
                                   title="أدخل النص الذي تريد البحث عنه">
                        </div>
                        <div class="col-md-6">
                            <label for="replace" class="form-label">
                                <i class="fas fa-exchange-alt me-1"></i>كلمة الاستبدال
                            </label>
                            <input type="text"
                                   id="replace"
                                   name="replace"
                                   class="form-control"
                                   placeholder="أدخل النص البديل (اختياري)..."
                                   value="<?php echo htmlspecialchars($_POST['replace'] ?? ''); ?>"
                                   data-bs-toggle="tooltip"
                                   title="النص الذي سيحل محل النص المبحوث عنه">
                        </div>
                    </div>

                    <!-- Text Content -->
                    <div class="form-group mb-3">
                        <label for="article" class="form-label">
                            <i class="fas fa-file-text me-1"></i>النص المراد البحث فيه *
                        </label>
                        <textarea id="article"
                                  name="article"
                                  class="form-control"
                                  rows="8"
                                  placeholder="الصق النص هنا أو ارفع ملف نصي..."
                                  required
                                  data-bs-toggle="tooltip"
                                  title="النص الذي تريد البحث فيه"><?php echo htmlspecialchars($_POST['article'] ?? ''); ?></textarea>
                    </div>

                    <!-- File Upload -->
                    <div class="form-group mb-3">
                        <label for="fileInput" class="form-label">
                            <i class="fas fa-upload me-1"></i>أو ارفع ملف نصي
                        </label>
                        <input type="file"
                               id="fileInput"
                               class="form-control"
                               accept=".txt,.html,.css,.js,.json,.md"
                               data-bs-toggle="tooltip"
                               title="ارفع ملف نصي (حد أقصى 1 ميجابايت)">
                    </div>

                    <!-- Options -->
                    <div class="options-section mb-3">
                        <h6><i class="fas fa-cog me-1"></i>خيارات متقدمة</h6>
                        <div class="options-grid">
                            <div class="option-group">
                                <input type="checkbox"
                                       id="case_sensitive"
                                       name="case_sensitive"
                                       class="form-check-input option-input"
                                       <?php echo isset($_POST['case_sensitive']) ? 'checked' : ''; ?>>
                                <label for="case_sensitive" class="form-check-label">
                                    حساس لحالة الأحرف
                                </label>
                            </div>
                            <div class="option-group">
                                <input type="checkbox"
                                       id="use_regex"
                                       name="use_regex"
                                       class="form-check-input option-input"
                                       <?php echo isset($_POST['use_regex']) ? 'checked' : ''; ?>>
                                <label for="use_regex" class="form-check-label">
                                    استخدام التعبيرات النمطية
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="row">
                        <div class="col-md-6 mb-2">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-search me-1"></i>بحث / استبدال
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button type="button" id="clearBtn" class="btn btn-secondary w-100">
                                <i class="fas fa-eraser me-1"></i>مسح
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button type="reset" class="btn btn-warning w-100">
                                <i class="fas fa-undo me-1"></i>إعادة تعيين
                            </button>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Real-time Statistics -->
            <div id="realTimeStats" class="statistics fade-in">
                <h5><i class="fas fa-chart-bar me-1"></i>إحصائيات فورية</h5>
                <p class="text-muted">أدخل نصاً لرؤية الإحصائيات</p>
            </div>

            <!-- Results Section -->
            <?php if ($results !== null): ?>
                <div class="results-section fade-in">
                    <div class="results-header">
                        <h5><i class="fas fa-chart-line me-1"></i>نتائج التحليل</h5>
                        <div>
                            <button id="exportTxt" class="btn btn-success btn-sm me-1">
                                <i class="fas fa-download me-1"></i>تصدير TXT
                            </button>
                            <button id="exportJson" class="btn btn-info btn-sm">
                                <i class="fas fa-download me-1"></i>تصدير JSON
                            </button>
                        </div>
                    </div>

                    <!-- Detailed Statistics -->
                    <div class="stats-grid mb-3">
                        <div class="stat-item">
                            <span class="stat-number"><?php echo number_format($results['statistics']['word_count']); ?></span>
                            <span class="stat-label">إجمالي الكلمات</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number"><?php echo number_format($results['statistics']['character_count']); ?></span>
                            <span class="stat-label">إجمالي الأحرف</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number"><?php echo number_format($results['occurrences']); ?></span>
                            <span class="stat-label">عدد التطابقات</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number"><?php echo $results['percentage']; ?>%</span>
                            <span class="stat-label">نسبة التطابق</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number"><?php echo number_format($results['statistics']['line_count']); ?></span>
                            <span class="stat-label">عدد الأسطر</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number"><?php echo number_format($results['statistics']['paragraph_count']); ?></span>
                            <span class="stat-label">عدد الفقرات</span>
                        </div>
                    </div>

                    <!-- Progress Bar -->
                    <div class="progress mb-3">
                        <div class="progress-bar"
                             style="width: <?php echo min($results['percentage'], 100); ?>%"
                             role="progressbar">
                            <?php echo $results['percentage']; ?>%
                        </div>
                    </div>

                    <!-- Results Content -->
                    <div class="results-content">
                        <?php if (!empty($_POST['replace']) && $results['replaced_text']): ?>
                            <?php echo $results['replaced_text']; ?>
                        <?php else: ?>
                            <?php echo $results['highlighted_text']; ?>
                        <?php endif; ?>
                    </div>

                    <!-- Positions Information -->
                    <?php if (!empty($results['positions'])): ?>
                        <div class="mt-3">
                            <h6><i class="fas fa-map-marker-alt me-1"></i>مواقع التطابقات</h6>
                            <div class="row">
                                <?php foreach (array_slice($results['positions'], 0, 10) as $index => $position): ?>
                                    <div class="col-md-6 mb-2">
                                        <small class="text-muted">
                                            التطابق <?php echo $index + 1; ?>: الموقع <?php echo $position['position']; ?> (السطر <?php echo $position['line']; ?>)
                                        </small>
                                    </div>
                                <?php endforeach; ?>
                                <?php if (count($results['positions']) > 10): ?>
                                    <div class="col-12">
                                        <small class="text-muted">... و <?php echo count($results['positions']) - 10; ?> تطابق آخر</small>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>

            <!-- Footer Info -->
            <div class="text-center mt-4 text-muted">
                <small>
                    <i class="fas fa-info-circle me-1"></i>
                    <?php echo APP_NAME; ?> v<?php echo APP_VERSION; ?> |
                    اضغط Ctrl+Enter للبحث | Ctrl+K للتركيز على البحث | Escape للمسح
                </small>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="assets/js/app.js"></script>
</body>
</html>