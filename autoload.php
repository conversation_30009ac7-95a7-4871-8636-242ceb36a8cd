<?php
/**
 * Autoloader for classes
 */

spl_autoload_register(function ($className) {
    $classFile = __DIR__ . '/classes/' . $className . '.php';
    
    if (file_exists($classFile)) {
        require_once $classFile;
        return true;
    }
    
    return false;
});

// Load configuration
require_once __DIR__ . '/config/config.php';

// Initialize error handler
ErrorHandler::init();

// Initialize cache
CacheManager::init();
