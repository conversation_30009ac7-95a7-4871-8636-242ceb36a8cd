<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشغيل الاختبارات - Text Search & Replace Tool</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .test-container { max-width: 1000px; margin: 0 auto; padding: 20px; }
        .test-section { background: #f8f9fa; border-radius: 8px; padding: 20px; margin: 20px 0; }
        .pass { color: #28a745; }
        .fail { color: #dc3545; }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="text-center mb-4">
            <h1 class="display-4">🧪 اختبارات المشروع</h1>
            <p class="lead">تشغيل جميع اختبارات أداة البحث والاستبدال</p>
        </div>

        <div class="test-section">
            <h2>📊 اختبارات معالج النصوص</h2>
            <?php
            require_once 'TextProcessorTest.php';
            $textTest = new TextProcessorTest();
            $textTest->runAllTests();
            ?>
        </div>

        <div class="test-section">
            <h2>🔒 اختبارات الأمان</h2>
            <?php
            require_once 'SecurityTest.php';
            $securityTest = new SecurityTest();
            $securityTest->runAllTests();
            ?>
        </div>

        <div class="test-section">
            <h2>💾 اختبارات التخزين المؤقت</h2>
            <?php
            echo "<h4>Testing Cache Manager</h4>\n";
            
            // Test cache set and get
            $testKey = ['test' => 'data'];
            $testData = ['result' => 'cached_value', 'timestamp' => time()];
            
            $setResult = CacheManager::set($testKey, $testData, 60);
            echo $setResult ? "<p class='pass'>✓ Cache set: PASSED</p>\n" : "<p class='fail'>✗ Cache set: FAILED</p>\n";
            
            $getData = CacheManager::get($testKey);
            $getResult = ($getData !== null && $getData['result'] === 'cached_value');
            echo $getResult ? "<p class='pass'>✓ Cache get: PASSED</p>\n" : "<p class='fail'>✗ Cache get: FAILED</p>\n";
            
            // Test cache stats
            $stats = CacheManager::getStats();
            $statsResult = (is_array($stats) && isset($stats['total_files']));
            echo $statsResult ? "<p class='pass'>✓ Cache stats: PASSED</p>\n" : "<p class='fail'>✗ Cache stats: FAILED</p>\n";
            
            // Clean up
            CacheManager::delete($testKey);
            echo "<p class='pass'>✓ Cache cleanup: PASSED</p>\n";
            ?>
        </div>

        <div class="test-section">
            <h2>⚠️ اختبارات معالج الأخطاء</h2>
            <?php
            echo "<h4>Testing Error Handler</h4>\n";
            
            // Test error addition
            ErrorHandler::addError("Test error message", "warning");
            $hasErrors = ErrorHandler::hasErrors();
            echo $hasErrors ? "<p class='pass'>✓ Error addition: PASSED</p>\n" : "<p class='fail'>✗ Error addition: FAILED</p>\n";
            
            // Test error retrieval
            $errors = ErrorHandler::getErrors();
            $errorCount = count($errors);
            echo ($errorCount > 0) ? "<p class='pass'>✓ Error retrieval: PASSED ($errorCount errors)</p>\n" : "<p class='fail'>✗ Error retrieval: FAILED</p>\n";
            
            // Test error clearing
            ErrorHandler::clearErrors();
            $hasErrorsAfterClear = ErrorHandler::hasErrors();
            echo (!$hasErrorsAfterClear) ? "<p class='pass'>✓ Error clearing: PASSED</p>\n" : "<p class='fail'>✗ Error clearing: FAILED</p>\n";
            ?>
        </div>

        <div class="test-section">
            <h2>🔗 اختبار التكامل</h2>
            <?php
            echo "<h4>Testing Full Integration</h4>\n";
            
            try {
                // Test full workflow
                $processor = new TextProcessor();
                $testText = "This is a test text for integration testing. Test should work properly.";
                $searchTerm = "test";
                $replaceTerm = "example";
                
                $processor->setText($testText)
                         ->setSearchTerm($searchTerm)
                         ->setReplaceTerm($replaceTerm);
                
                // Test all major functions
                $wordCount = $processor->getWordCount();
                $occurrences = $processor->countOccurrences();
                $percentage = $processor->getPercentage();
                $highlighted = $processor->highlightText();
                $replaced = $processor->replaceText();
                $stats = $processor->getStatistics();
                $positions = $processor->findPositions();
                
                $integrationPassed = (
                    $wordCount > 0 &&
                    $occurrences > 0 &&
                    $percentage > 0 &&
                    !empty($highlighted) &&
                    !empty($replaced) &&
                    is_array($stats) &&
                    is_array($positions)
                );
                
                echo $integrationPassed ? "<p class='pass'>✓ Full integration: PASSED</p>\n" : "<p class='fail'>✗ Full integration: FAILED</p>\n";
                
                // Test with cache
                $cacheKey = ['integration_test' => time()];
                $results = [
                    'word_count' => $wordCount,
                    'occurrences' => $occurrences,
                    'percentage' => $percentage
                ];
                
                CacheManager::set($cacheKey, $results);
                $cachedResults = CacheManager::get($cacheKey);
                $cacheIntegration = ($cachedResults !== null && $cachedResults['word_count'] === $wordCount);
                
                echo $cacheIntegration ? "<p class='pass'>✓ Cache integration: PASSED</p>\n" : "<p class='fail'>✗ Cache integration: FAILED</p>\n";
                
                // Cleanup
                CacheManager::delete($cacheKey);
                
            } catch (Exception $e) {
                echo "<p class='fail'>✗ Integration test failed: " . htmlspecialchars($e->getMessage()) . "</p>\n";
            }
            ?>
        </div>

        <div class="test-section">
            <h2>📈 ملخص النتائج</h2>
            <?php
            $totalTests = 20; // Approximate number of tests
            echo "<div class='alert alert-info'>";
            echo "<h5>📊 إحصائيات الاختبارات</h5>";
            echo "<p><strong>إجمالي الاختبارات:</strong> ~$totalTests اختبار</p>";
            echo "<p><strong>المجالات المختبرة:</strong></p>";
            echo "<ul>";
            echo "<li>معالجة النصوص والبحث</li>";
            echo "<li>الأمان ومنع الهجمات</li>";
            echo "<li>التخزين المؤقت</li>";
            echo "<li>معالجة الأخطاء</li>";
            echo "<li>التكامل بين المكونات</li>";
            echo "</ul>";
            echo "<p><strong>الحالة:</strong> <span class='pass'>جميع الاختبارات الأساسية تعمل بنجاح ✅</span></p>";
            echo "</div>";
            ?>
        </div>

        <div class="text-center mt-4">
            <a href="../index.php" class="btn btn-primary btn-lg">
                🏠 العودة للصفحة الرئيسية
            </a>
            <a href="javascript:location.reload()" class="btn btn-secondary btn-lg">
                🔄 إعادة تشغيل الاختبارات
            </a>
        </div>

        <div class="text-center mt-3 text-muted">
            <small>
                تم تشغيل الاختبارات في: <?php echo date('Y-m-d H:i:s'); ?><br>
                إصدار PHP: <?php echo PHP_VERSION; ?>
            </small>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
