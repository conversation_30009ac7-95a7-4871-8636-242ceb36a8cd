/* Enhanced Text Search & Replace Tool Styles */

:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --light-color: #ecf0f1;
    --dark-color: #2c3e50;
    --border-radius: 8px;
    --box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    --transition: all 0.3s ease;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    margin: 0;
    padding: 20px;
}

.main-container {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
    max-width: 1000px;
    margin: 0 auto;
}

.header {
    background: var(--primary-color);
    color: white;
    padding: 20px;
    text-align: center;
}

.header h1 {
    margin: 0;
    font-size: 2rem;
    font-weight: 300;
}

.header p {
    margin: 5px 0 0 0;
    opacity: 0.8;
    font-size: 0.9rem;
}

.content {
    padding: 30px;
}

/* Form Styles */
.form-section {
    background: #f8f9fa;
    border-radius: var(--border-radius);
    padding: 25px;
    margin-bottom: 30px;
    border: 1px solid #e9ecef;
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 8px;
    display: block;
}

.form-control {
    border: 2px solid #e9ecef;
    border-radius: var(--border-radius);
    padding: 12px 15px;
    font-size: 14px;
    transition: var(--transition);
    width: 100%;
    box-sizing: border-box;
}

.form-control:focus {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
    outline: none;
}

.form-control.is-invalid {
    border-color: var(--danger-color);
}

.form-control.is-valid {
    border-color: var(--success-color);
}

/* Button Styles */
.btn {
    padding: 12px 25px;
    border-radius: var(--border-radius);
    border: none;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: var(--transition);
    cursor: pointer;
    font-size: 14px;
}

.btn-primary {
    background: var(--secondary-color);
    color: white;
}

.btn-primary:hover {
    background: #2980b9;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.btn-success {
    background: var(--success-color);
    color: white;
}

.btn-success:hover {
    background: #229954;
    transform: translateY(-2px);
}

.btn-warning {
    background: var(--warning-color);
    color: white;
}

.btn-warning:hover {
    background: #e67e22;
    transform: translateY(-2px);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

/* Statistics Section */
.statistics {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: var(--border-radius);
    padding: 25px;
    margin: 20px 0;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.stat-item {
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius);
    padding: 20px;
    text-align: center;
    backdrop-filter: blur(10px);
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    display: block;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
    margin-top: 5px;
}

/* Progress Bar */
.progress {
    height: 8px;
    border-radius: 4px;
    background: #e9ecef;
    overflow: hidden;
    margin: 15px 0;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--success-color), var(--secondary-color));
    border-radius: 4px;
    transition: width 0.6s ease;
}

/* Results Section */
.results-section {
    background: #f8f9fa;
    border-radius: var(--border-radius);
    padding: 25px;
    margin: 20px 0;
    border: 1px solid #e9ecef;
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 10px;
}

.results-content {
    background: white;
    border-radius: var(--border-radius);
    padding: 20px;
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #e9ecef;
    font-family: 'Courier New', monospace;
    line-height: 1.6;
    white-space: pre-wrap;
    word-wrap: break-word;
}

/* Highlight Styles */
.text-highlight {
    background: linear-gradient(120deg, #f39c12 0%, #e74c3c 100%);
    color: white;
    padding: 2px 4px;
    border-radius: 3px;
    font-weight: bold;
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
}

mark {
    background: linear-gradient(120deg, #f39c12 0%, #e74c3c 100%);
    color: white;
    padding: 2px 4px;
    border-radius: 3px;
    font-weight: bold;
}

/* Options Section */
.options-section {
    background: #f8f9fa;
    border-radius: var(--border-radius);
    padding: 20px;
    margin: 20px 0;
    border: 1px solid #e9ecef;
}

.options-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.option-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.form-check-input {
    width: 18px;
    height: 18px;
    margin: 0;
}

.form-check-label {
    font-weight: 500;
    color: var(--dark-color);
    cursor: pointer;
}

/* Alert Styles */
.alert {
    padding: 15px 20px;
    border-radius: var(--border-radius);
    margin: 15px 0;
    border: none;
    font-weight: 500;
}

.alert-success {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    color: white;
}

.alert-warning {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    color: white;
}

.alert-danger {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
}

.alert-info {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    body {
        padding: 10px;
    }
    
    .content {
        padding: 20px;
    }
    
    .header h1 {
        font-size: 1.5rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .results-header {
        flex-direction: column;
        align-items: stretch;
    }
    
    .btn {
        width: 100%;
        margin-bottom: 10px;
    }
}

/* Scrollbar Styling */
.results-content::-webkit-scrollbar {
    width: 8px;
}

.results-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.results-content::-webkit-scrollbar-thumb {
    background: var(--secondary-color);
    border-radius: 4px;
}

.results-content::-webkit-scrollbar-thumb:hover {
    background: #2980b9;
}

/* Tooltip */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltiptext {
    visibility: hidden;
    width: 200px;
    background-color: var(--dark-color);
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 8px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -100px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 12px;
}

.tooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}
