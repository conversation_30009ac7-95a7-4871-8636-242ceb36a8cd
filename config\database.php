<?php
/**
 * Database Configuration
 * For future database features
 */

// Database configuration (uncomment when ready to use)
/*
define('DB_HOST', 'localhost');
define('DB_NAME', 'text_search_replace');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
define('DB_CHARSET', 'utf8mb4');
define('DB_COLLATE', 'utf8mb4_unicode_ci');

// PDO options
define('DB_OPTIONS', [
    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    PDO::ATTR_EMULATE_PREPARES => false,
    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES " . DB_CHARSET . " COLLATE " . DB_COLLATE
]);
*/

/**
 * Database Connection Class
 */
class Database
{
    private static $instance = null;
    private $connection;
    
    private function __construct()
    {
        // Uncomment when database is configured
        /*
        try {
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
            $this->connection = new PDO($dsn, DB_USER, DB_PASS, DB_OPTIONS);
        } catch (PDOException $e) {
            ErrorHandler::addError("Database connection failed: " . $e->getMessage(), 'error');
            throw new Exception("Database connection failed");
        }
        */
    }
    
    public static function getInstance()
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    public function getConnection()
    {
        return $this->connection;
    }
    
    /**
     * Execute a query
     */
    public function query($sql, $params = [])
    {
        if ($this->connection === null) {
            throw new Exception("Database not configured");
        }
        
        try {
            $stmt = $this->connection->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            ErrorHandler::addError("Database query failed: " . $e->getMessage(), 'error');
            throw new Exception("Database query failed");
        }
    }
    
    /**
     * Get single record
     */
    public function fetch($sql, $params = [])
    {
        $stmt = $this->query($sql, $params);
        return $stmt->fetch();
    }
    
    /**
     * Get multiple records
     */
    public function fetchAll($sql, $params = [])
    {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchAll();
    }
    
    /**
     * Insert record and return last insert ID
     */
    public function insert($sql, $params = [])
    {
        $this->query($sql, $params);
        return $this->connection->lastInsertId();
    }
    
    /**
     * Update/Delete and return affected rows
     */
    public function execute($sql, $params = [])
    {
        $stmt = $this->query($sql, $params);
        return $stmt->rowCount();
    }
    
    /**
     * Begin transaction
     */
    public function beginTransaction()
    {
        if ($this->connection) {
            return $this->connection->beginTransaction();
        }
        return false;
    }
    
    /**
     * Commit transaction
     */
    public function commit()
    {
        if ($this->connection) {
            return $this->connection->commit();
        }
        return false;
    }
    
    /**
     * Rollback transaction
     */
    public function rollback()
    {
        if ($this->connection) {
            return $this->connection->rollback();
        }
        return false;
    }
}

/**
 * Model Base Class
 */
abstract class BaseModel
{
    protected $db;
    protected $table;
    
    public function __construct()
    {
        $this->db = Database::getInstance();
    }
    
    /**
     * Find record by ID
     */
    public function find($id)
    {
        $sql = "SELECT * FROM {$this->table} WHERE id = ?";
        return $this->db->fetch($sql, [$id]);
    }
    
    /**
     * Find all records
     */
    public function findAll($limit = null, $offset = 0)
    {
        $sql = "SELECT * FROM {$this->table}";
        if ($limit) {
            $sql .= " LIMIT {$limit} OFFSET {$offset}";
        }
        return $this->db->fetchAll($sql);
    }
    
    /**
     * Create new record
     */
    public function create($data)
    {
        $fields = array_keys($data);
        $placeholders = array_fill(0, count($fields), '?');
        
        $sql = "INSERT INTO {$this->table} (" . implode(', ', $fields) . ") VALUES (" . implode(', ', $placeholders) . ")";
        
        return $this->db->insert($sql, array_values($data));
    }
    
    /**
     * Update record
     */
    public function update($id, $data)
    {
        $fields = array_keys($data);
        $setClause = implode(' = ?, ', $fields) . ' = ?';
        
        $sql = "UPDATE {$this->table} SET {$setClause} WHERE id = ?";
        $params = array_merge(array_values($data), [$id]);
        
        return $this->db->execute($sql, $params);
    }
    
    /**
     * Delete record
     */
    public function delete($id)
    {
        $sql = "DELETE FROM {$this->table} WHERE id = ?";
        return $this->db->execute($sql, [$id]);
    }
    
    /**
     * Count records
     */
    public function count($where = null, $params = [])
    {
        $sql = "SELECT COUNT(*) as count FROM {$this->table}";
        if ($where) {
            $sql .= " WHERE {$where}";
        }
        
        $result = $this->db->fetch($sql, $params);
        return $result['count'] ?? 0;
    }
}

/**
 * Example Models (for future use)
 */

class SearchHistory extends BaseModel
{
    protected $table = 'search_history';
    
    public function getByUser($userId, $limit = 50)
    {
        $sql = "SELECT * FROM {$this->table} WHERE user_id = ? ORDER BY created_at DESC LIMIT ?";
        return $this->db->fetchAll($sql, [$userId, $limit]);
    }
    
    public function getPopularSearches($days = 30, $limit = 10)
    {
        $sql = "SELECT search_term, COUNT(*) as count 
                FROM {$this->table} 
                WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
                GROUP BY search_term 
                ORDER BY count DESC 
                LIMIT ?";
        return $this->db->fetchAll($sql, [$days, $limit]);
    }
}

class SavedSearches extends BaseModel
{
    protected $table = 'saved_searches';
    
    public function getByUser($userId)
    {
        $sql = "SELECT * FROM {$this->table} WHERE user_id = ? ORDER BY name";
        return $this->db->fetchAll($sql, [$userId]);
    }
}

class TextTemplates extends BaseModel
{
    protected $table = 'text_templates';
    
    public function getPublicTemplates($limit = 20)
    {
        $sql = "SELECT * FROM {$this->table} WHERE is_public = 1 ORDER BY created_at DESC LIMIT ?";
        return $this->db->fetchAll($sql, [$limit]);
    }
    
    public function getByUser($userId)
    {
        $sql = "SELECT * FROM {$this->table} WHERE user_id = ? ORDER BY name";
        return $this->db->fetchAll($sql, [$userId]);
    }
}
