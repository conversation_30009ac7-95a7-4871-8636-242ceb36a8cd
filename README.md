# أداة البحث والاستبدال المتقدمة - Text Search & Replace Tool

أداة ويب متقدمة للبحث والاستبدال في النصوص مع إحصائيات مفصلة ودعم التعبيرات النمطية.

## 🚀 التشغيل السريع

```bash
# تشغيل الخادم المحلي
php serve.php

# أو باستخدام خادم PHP المدمج
php -S localhost:8000

# ثم افتح المتصفح على
http://localhost:8000
```

### اختبار سريع
```bash
# تشغيل جميع الاختبارات
php tests/run_tests.php

# أو في المتصفح
http://localhost:8000/tests/run_tests.php
```

## 🌟 الميزات الجديدة

### 🔒 الأمان المحسن
- حماية من هجمات XSS و CSRF
- تنظيف وتعقيم جميع المدخلات
- نظام Rate Limiting لمنع الإفراط في الاستخدام
- تسجيل الأحداث الأمنية

### 🏗️ الهيكل المحسن
- هيكل MVC منظم مع فصل المنطق عن العرض
- نظام Autoloading للكلاسات
- إدارة مركزية للإعدادات
- معالجة احترافية للأخطاء

### ⚡ الأداء المحسن
- نظام Cache ذكي لتسريع النتائج
- خوارزميات محسنة للبحث والاستبدال
- تحسين استهلاك الذاكرة

### 🎨 واجهة المستخدم المحسنة
- تصميم عصري ومتجاوب
- دعم اللغة العربية مع RTL
- إحصائيات فورية أثناء الكتابة
- اختصارات لوحة المفاتيح

### 🔧 ميزات متقدمة
- دعم التعبيرات النمطية (Regex)
- رفع الملفات النصية
- تصدير النتائج (TXT/JSON)
- إحصائيات مفصلة
- حفظ تلقائي للإعدادات

## 📁 هيكل المشروع

```
php_project/
├── index.php              # الصفحة الرئيسية
├── autoload.php           # محمل الكلاسات التلقائي
├── README.md              # ملف التوثيق
├── config/
│   └── config.php         # ملف الإعدادات
├── classes/
│   ├── TextProcessor.php  # معالج النصوص الرئيسي
│   ├── SecurityManager.php # مدير الأمان
│   ├── CacheManager.php   # مدير التخزين المؤقت
│   └── ErrorHandler.php   # معالج الأخطاء
├── assets/
│   ├── css/
│   │   └── style.css      # ملف التنسيقات المخصص
│   └── js/
│       └── app.js         # ملف JavaScript المخصص
├── cache/                 # مجلد التخزين المؤقت
└── logs/                  # مجلد سجلات الأخطاء
```

## 🚀 التثبيت والإعداد

### المتطلبات
- PHP 7.4 أو أحدث
- خادم ويب (Apache/Nginx)
- دعم sessions في PHP

### خطوات التثبيت

1. **نسخ الملفات**
   ```bash
   git clone [repository-url]
   cd php_project
   ```

2. **إعداد الصلاحيات**
   ```bash
   chmod 755 cache/
   chmod 755 logs/
   ```

3. **تخصيص الإعدادات**
   - افتح `config/config.php`
   - عدل الإعدادات حسب احتياجاتك

4. **تشغيل الخادم**
   ```bash
   php -S localhost:8000
   ```

## 🎯 كيفية الاستخدام

### البحث البسيط
1. أدخل النص في المنطقة المخصصة
2. أدخل كلمة البحث
3. اضغط "بحث / استبدال"

### البحث والاستبدال
1. أدخل النص والكلمة المراد البحث عنها
2. أدخل النص البديل
3. اضغط "بحث / استبدال"

### الخيارات المتقدمة
- **حساس لحالة الأحرف**: للتمييز بين الأحرف الكبيرة والصغيرة
- **التعبيرات النمطية**: لاستخدام أنماط البحث المتقدمة

### رفع الملفات
- اضغط على "ارفع ملف نصي"
- اختر ملف نصي (حد أقصى 1 ميجابايت)
- سيتم تحميل المحتوى تلقائياً

### تصدير النتائج
- اضغط "تصدير TXT" لحفظ النتائج كملف نصي
- اضغط "تصدير JSON" لحفظ البيانات مع الإحصائيات

## ⌨️ اختصارات لوحة المفاتيح

- `Ctrl + Enter`: تشغيل البحث
- `Ctrl + K`: التركيز على حقل البحث
- `Escape`: مسح النموذج

## 🔧 الإعدادات المتقدمة

### ملف config/config.php

```php
// الحد الأقصى لطول النص
define('MAX_TEXT_LENGTH', 100000);

// تفعيل التخزين المؤقت
define('CACHE_ENABLED', true);
define('CACHE_DURATION', 300);

// إعدادات الأمان
define('SESSION_TIMEOUT', 3600);

// تسجيل الأخطاء
define('DEBUG_MODE', true);
define('LOG_ERRORS', true);
```

## 🛡️ الأمان

### الحماية المطبقة
- **CSRF Protection**: حماية من هجمات Cross-Site Request Forgery
- **XSS Prevention**: تنظيف جميع المدخلات
- **Rate Limiting**: منع الإفراط في الاستخدام
- **Input Validation**: التحقق من صحة البيانات
- **Secure File Upload**: رفع آمن للملفات

### أفضل الممارسات
- تحديث PHP بانتظام
- استخدام HTTPS في الإنتاج
- مراجعة سجلات الأخطاء
- تحديد صلاحيات الملفات بدقة

## 📊 الإحصائيات المتاحة

- إجمالي الكلمات
- إجمالي الأحرف (مع وبدون المسافات)
- عدد الأسطر والفقرات
- عدد التطابقات
- نسبة التطابق
- مواقع التطابقات مع أرقام الأسطر

## 🔍 دعم التعبيرات النمطية

### أمثلة على الأنماط
- `\\d+`: البحث عن الأرقام
- `\\w+@\\w+\\.\\w+`: البحث عن عناوين البريد الإلكتروني
- `^\\w+`: الكلمات في بداية السطر
- `\\w+$`: الكلمات في نهاية السطر

### احتياطات الأمان
- التحقق من صحة الأنماط
- منع الأنماط الخطيرة
- حد زمني لتنفيذ الأنماط المعقدة

## 🚨 استكشاف الأخطاء

### الأخطاء الشائعة

1. **خطأ في التخزين المؤقت**
   - تأكد من صلاحيات مجلد `cache/`
   - امسح الملفات المؤقتة يدوياً

2. **خطأ في رفع الملفات**
   - تحقق من حجم الملف (أقل من 1 ميجابايت)
   - تأكد من نوع الملف المدعوم

3. **خطأ في التعبيرات النمطية**
   - تحقق من صحة النمط
   - استخدم أدوات اختبار Regex

### سجلات الأخطاء
- تحقق من ملف `logs/error.log`
- فعل `DEBUG_MODE` للحصول على تفاصيل أكثر

## 🔄 التحديثات المستقبلية

### الميزات المخططة
- دعم المزيد من تنسيقات الملفات
- واجهة برمجة تطبيقات (API)
- نظام المستخدمين والصلاحيات
- تحليل متقدم للنصوص
- دعم قواعد البيانات

### المساهمة
نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة
3. إرسال Pull Request

## 📝 الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف LICENSE للتفاصيل.

## 📞 الدعم

للحصول على الدعم:
- افتح Issue في GitHub
- راجع قسم استكشاف الأخطاء
- تحقق من سجلات الأخطاء

---

**تم تطوير هذا المشروع بواسطة AI Assistant مع التركيز على الأمان والأداء وسهولة الاستخدام.**
