<?php
/**
 * TextProcessor Class - Enhanced text processing functionality
 */

class TextProcessor
{
    private $text;
    private $searchTerm;
    private $replaceTerm;
    private $caseSensitive;
    private $useRegex;
    
    public function __construct($text = '', $caseSensitive = false, $useRegex = false)
    {
        $this->text = $this->sanitizeInput($text);
        $this->caseSensitive = $caseSensitive;
        $this->useRegex = $useRegex;
    }
    
    /**
     * Sanitize input to prevent XSS attacks
     */
    private function sanitizeInput($input)
    {
        return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
    }
    
    /**
     * Set text content
     */
    public function setText($text)
    {
        $this->text = $this->sanitizeInput($text);
        return $this;
    }
    
    /**
     * Set search term
     */
    public function setSearchTerm($searchTerm)
    {
        $this->searchTerm = $this->sanitizeInput($searchTerm);
        return $this;
    }
    
    /**
     * Set replace term
     */
    public function setReplaceTerm($replaceTerm)
    {
        $this->replaceTerm = $this->sanitizeInput($replaceTerm);
        return $this;
    }
    
    /**
     * Count total words in text
     */
    public function getWordCount($text = null)
    {
        $targetText = $text ?? $this->text;
        return str_word_count(strip_tags($targetText));
    }
    
    /**
     * Count character count
     */
    public function getCharacterCount($text = null)
    {
        $targetText = $text ?? $this->text;
        return mb_strlen(strip_tags($targetText), 'UTF-8');
    }
    
    /**
     * Count occurrences of search term
     */
    public function countOccurrences($text = null, $searchTerm = null)
    {
        $targetText = $text ?? $this->text;
        $search = $searchTerm ?? $this->searchTerm;
        
        if (empty($targetText) || empty($search)) {
            return 0;
        }
        
        if ($this->useRegex) {
            return $this->countRegexMatches($targetText, $search);
        }
        
        if ($this->caseSensitive) {
            return substr_count($targetText, $search);
        } else {
            return substr_count(strtolower($targetText), strtolower($search));
        }
    }
    
    /**
     * Count regex matches
     */
    private function countRegexMatches($text, $pattern)
    {
        try {
            $flags = $this->caseSensitive ? 0 : PREG_CASE_INSENSITIVE;
            return preg_match_all($pattern . ($flags ? 'i' : ''), $text);
        } catch (Exception $e) {
            error_log("Regex error: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Get percentage of search term in text
     */
    public function getPercentage($text = null, $searchTerm = null)
    {
        $targetText = $text ?? $this->text;
        $search = $searchTerm ?? $this->searchTerm;
        
        $wordCount = $this->getWordCount($targetText);
        if ($wordCount === 0) {
            return 0;
        }
        
        $occurrences = $this->countOccurrences($targetText, $search);
        return round(($occurrences / $wordCount) * 100, 2);
    }
    
    /**
     * Highlight search term in text
     */
    public function highlightText($text = null, $searchTerm = null, $highlightClass = 'highlight')
    {
        $targetText = $text ?? $this->text;
        $search = $searchTerm ?? $this->searchTerm;
        
        if (empty($targetText) || empty($search)) {
            return $targetText;
        }
        
        if ($this->useRegex) {
            return $this->highlightRegex($targetText, $search, $highlightClass);
        }
        
        $replacement = "<mark class=\"{$highlightClass}\">{$search}</mark>";
        
        if ($this->caseSensitive) {
            return str_replace($search, $replacement, $targetText);
        } else {
            return str_ireplace($search, $replacement, $targetText);
        }
    }
    
    /**
     * Highlight regex matches
     */
    private function highlightRegex($text, $pattern, $highlightClass)
    {
        try {
            $flags = $this->caseSensitive ? 0 : PREG_CASE_INSENSITIVE;
            return preg_replace(
                $pattern . ($flags ? 'i' : ''),
                "<mark class=\"{$highlightClass}\">$0</mark>",
                $text
            );
        } catch (Exception $e) {
            error_log("Regex highlight error: " . $e->getMessage());
            return $text;
        }
    }
    
    /**
     * Replace text
     */
    public function replaceText($text = null, $searchTerm = null, $replaceTerm = null)
    {
        $targetText = $text ?? $this->text;
        $search = $searchTerm ?? $this->searchTerm;
        $replace = $replaceTerm ?? $this->replaceTerm;
        
        if (empty($targetText) || empty($search)) {
            return $targetText;
        }
        
        if ($this->useRegex) {
            return $this->replaceRegex($targetText, $search, $replace);
        }
        
        if ($this->caseSensitive) {
            return str_replace($search, $replace, $targetText);
        } else {
            return str_ireplace($search, $replace, $targetText);
        }
    }
    
    /**
     * Replace using regex
     */
    private function replaceRegex($text, $pattern, $replacement)
    {
        try {
            $flags = $this->caseSensitive ? 0 : PREG_CASE_INSENSITIVE;
            return preg_replace($pattern . ($flags ? 'i' : ''), $replacement, $text);
        } catch (Exception $e) {
            error_log("Regex replace error: " . $e->getMessage());
            return $text;
        }
    }
    
    /**
     * Get text statistics
     */
    public function getStatistics($text = null)
    {
        $targetText = $text ?? $this->text;
        
        return [
            'word_count' => $this->getWordCount($targetText),
            'character_count' => $this->getCharacterCount($targetText),
            'character_count_no_spaces' => $this->getCharacterCount(str_replace(' ', '', $targetText)),
            'paragraph_count' => substr_count($targetText, "\n\n") + 1,
            'line_count' => substr_count($targetText, "\n") + 1,
            'sentence_count' => preg_match_all('/[.!?]+/', $targetText),
        ];
    }
    
    /**
     * Find all positions of search term
     */
    public function findPositions($text = null, $searchTerm = null)
    {
        $targetText = $text ?? $this->text;
        $search = $searchTerm ?? $this->searchTerm;
        
        if (empty($targetText) || empty($search)) {
            return [];
        }
        
        $positions = [];
        $offset = 0;
        
        while (($pos = stripos($targetText, $search, $offset)) !== false) {
            $positions[] = [
                'position' => $pos,
                'line' => substr_count(substr($targetText, 0, $pos), "\n") + 1,
                'context' => $this->getContext($targetText, $pos, strlen($search))
            ];
            $offset = $pos + 1;
        }
        
        return $positions;
    }
    
    /**
     * Get context around a position
     */
    private function getContext($text, $position, $length, $contextLength = 50)
    {
        $start = max(0, $position - $contextLength);
        $end = min(strlen($text), $position + $length + $contextLength);
        
        return [
            'before' => substr($text, $start, $position - $start),
            'match' => substr($text, $position, $length),
            'after' => substr($text, $position + $length, $end - ($position + $length))
        ];
    }
}
