{"name": "enhanced/text-search-replace", "description": "Enhanced Text Search & Replace Tool with advanced features, security, and performance optimizations", "type": "project", "keywords": ["text", "search", "replace", "php", "tool", "regex", "arabic"], "license": "MIT", "authors": [{"name": "AI Assistant", "email": "<EMAIL>", "role": "Developer"}], "require": {"php": ">=7.4.0", "ext-mbstring": "*", "ext-json": "*", "ext-session": "*"}, "require-dev": {"phpunit/phpunit": "^9.0", "squizlabs/php_codesniffer": "^3.6"}, "autoload": {"psr-4": {"TextSearchReplace\\": "classes/"}, "files": ["autoload.php"]}, "autoload-dev": {"psr-4": {"TextSearchReplace\\Tests\\": "tests/"}}, "scripts": {"test": "php tests/run_tests.php", "check-style": "phpcs --standard=PSR12 classes/", "fix-style": "phpcbf --standard=PSR12 classes/", "serve": "php -S localhost:8000 -t .", "clear-cache": "php -r \"array_map('unlink', glob('cache/*.cache'));\""}, "config": {"optimize-autoloader": true, "sort-packages": true, "allow-plugins": {"composer/package-versions-deprecated": true}}, "minimum-stability": "stable", "prefer-stable": true, "extra": {"branch-alias": {"dev-main": "2.0-dev"}}, "support": {"issues": "https://github.com/username/text-search-replace/issues", "source": "https://github.com/username/text-search-replace"}}