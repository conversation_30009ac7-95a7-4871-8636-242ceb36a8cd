<?php
/**
 * ErrorHandler Class - Centralized error handling
 */

class ErrorHandler
{
    private static $errors = [];
    
    /**
     * Initialize error handler
     */
    public static function init()
    {
        // Set custom error handler
        set_error_handler([self::class, 'handleError']);
        set_exception_handler([self::class, 'handleException']);
        
        // Create logs directory if it doesn't exist
        $logDir = dirname(LOG_FILE);
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
    }
    
    /**
     * Handle PHP errors
     */
    public static function handleError($severity, $message, $file, $line)
    {
        $error = [
            'type' => 'error',
            'severity' => $severity,
            'message' => $message,
            'file' => $file,
            'line' => $line,
            'timestamp' => date('Y-m-d H:i:s'),
            'trace' => debug_backtrace()
        ];
        
        self::logError($error);
        
        if (DEBUG_MODE) {
            self::$errors[] = $error;
        }
        
        // Don't execute PHP internal error handler
        return true;
    }
    
    /**
     * Handle uncaught exceptions
     */
    public static function handleException($exception)
    {
        $error = [
            'type' => 'exception',
            'message' => $exception->getMessage(),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'timestamp' => date('Y-m-d H:i:s'),
            'trace' => $exception->getTrace()
        ];
        
        self::logError($error);
        
        if (DEBUG_MODE) {
            self::displayError($error);
        } else {
            self::displayGenericError();
        }
    }
    
    /**
     * Log error to file
     */
    private static function logError($error)
    {
        if (LOG_ERRORS) {
            $logMessage = sprintf(
                "[%s] %s: %s in %s on line %d\n",
                $error['timestamp'],
                strtoupper($error['type']),
                $error['message'],
                $error['file'],
                $error['line']
            );
            
            error_log($logMessage, 3, LOG_FILE);
        }
    }
    
    /**
     * Display error for debugging
     */
    private static function displayError($error)
    {
        echo "<div class='alert alert-danger'>";
        echo "<h4>Error Details:</h4>";
        echo "<p><strong>Type:</strong> " . htmlspecialchars($error['type']) . "</p>";
        echo "<p><strong>Message:</strong> " . htmlspecialchars($error['message']) . "</p>";
        echo "<p><strong>File:</strong> " . htmlspecialchars($error['file']) . "</p>";
        echo "<p><strong>Line:</strong> " . $error['line'] . "</p>";
        echo "<p><strong>Time:</strong> " . $error['timestamp'] . "</p>";
        echo "</div>";
    }
    
    /**
     * Display generic error message
     */
    private static function displayGenericError()
    {
        echo "<div class='alert alert-danger'>";
        echo "<h4>An Error Occurred</h4>";
        echo "<p>We're sorry, but something went wrong. Please try again later.</p>";
        echo "</div>";
    }
    
    /**
     * Add custom error
     */
    public static function addError($message, $type = 'warning')
    {
        self::$errors[] = [
            'type' => $type,
            'message' => $message,
            'timestamp' => date('Y-m-d H:i:s')
        ];
    }
    
    /**
     * Get all errors
     */
    public static function getErrors()
    {
        return self::$errors;
    }
    
    /**
     * Check if there are errors
     */
    public static function hasErrors()
    {
        return !empty(self::$errors);
    }
    
    /**
     * Clear all errors
     */
    public static function clearErrors()
    {
        self::$errors = [];
    }
    
    /**
     * Display all errors
     */
    public static function displayErrors()
    {
        if (empty(self::$errors)) {
            return;
        }
        
        foreach (self::$errors as $error) {
            $alertClass = 'alert-warning';
            switch ($error['type']) {
                case 'error':
                case 'exception':
                    $alertClass = 'alert-danger';
                    break;
                case 'success':
                    $alertClass = 'alert-success';
                    break;
                case 'info':
                    $alertClass = 'alert-info';
                    break;
            }
            
            echo "<div class='alert {$alertClass} alert-dismissible fade show' role='alert'>";
            echo htmlspecialchars($error['message']);
            echo "<button type='button' class='btn-close' data-bs-dismiss='alert'></button>";
            echo "</div>";
        }
    }
}
