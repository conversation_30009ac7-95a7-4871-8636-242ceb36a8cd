# Text Search & Replace Tool - Git Ignore File

# Cache files
cache/*.cache
cache/*
!cache/.gitkeep

# Log files
logs/*.log
logs/*
!logs/.gitkeep

# Temporary files
*.tmp
*.temp
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE and Editor files
.vscode/
.idea/
*.sublime-project
*.sublime-workspace
*.code-workspace

# PHP specific
*.php~
*.php.bak
*.php.backup

# Composer
vendor/
composer.lock
composer.phar

# Environment files
.env
.env.local
.env.production
.env.test

# Database files (if using SQLite)
*.sqlite
*.sqlite3
*.db

# Backup files
*.bak
*.backup
*.old

# Upload directories (if implemented)
uploads/*
!uploads/.gitkeep

# Configuration overrides
config/local.php
config/production.php
config/development.php

# Session files
sessions/*
!sessions/.gitkeep

# Error logs
error.log
php_errors.log

# Apache/Nginx logs
access.log
error.log

# Node modules (if using any frontend tools)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build directories
build/
dist/

# Coverage reports
coverage/
*.coverage

# PHPUnit
.phpunit.result.cache
phpunit.xml

# Deployment files
deploy.php
deploy.sh
.deploy/

# Documentation build
docs/_build/

# Local development files
local/
dev/
development/

# Archive files
*.zip
*.tar.gz
*.rar
*.7z

# Large files that shouldn't be in git
*.mp4
*.avi
*.mov
*.wmv
*.flv
*.webm

# Sensitive files
secrets.php
keys.php
passwords.txt
credentials.json

# Test files
test_data/
sample_files/

# Performance profiling
*.prof
*.xhprof

# Docker
.docker/
docker-compose.override.yml

# Vagrant
.vagrant/

# Local server files
serve.log
server.log
