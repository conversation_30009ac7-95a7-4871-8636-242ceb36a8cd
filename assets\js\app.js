/**
 * Enhanced Text Search & Replace Tool - JavaScript
 */

class TextSearchReplace {
    constructor() {
        this.init();
        this.bindEvents();
        this.loadSettings();
    }

    init() {
        // Initialize tooltips
        this.initTooltips();
        
        // Initialize auto-save
        this.initAutoSave();
        
        // Initialize keyboard shortcuts
        this.initKeyboardShortcuts();
        
        // Initialize real-time statistics
        this.initRealTimeStats();
        
        // Initialize form validation
        this.initFormValidation();
    }

    bindEvents() {
        // Form submission
        const form = document.getElementById('searchForm');
        if (form) {
            form.addEventListener('submit', (e) => this.handleFormSubmit(e));
        }

        // Real-time search
        const searchInput = document.getElementById('search');
        const articleInput = document.getElementById('article');
        
        if (searchInput && articleInput) {
            searchInput.addEventListener('input', () => this.debounce(this.updateRealTimeStats.bind(this), 300)());
            articleInput.addEventListener('input', () => this.debounce(this.updateRealTimeStats.bind(this), 300)());
        }

        // Options change
        const optionInputs = document.querySelectorAll('.option-input');
        optionInputs.forEach(input => {
            input.addEventListener('change', () => this.saveSettings());
        });

        // Clear button
        const clearBtn = document.getElementById('clearBtn');
        if (clearBtn) {
            clearBtn.addEventListener('click', () => this.clearForm());
        }

        // Export buttons
        const exportTxtBtn = document.getElementById('exportTxt');
        const exportJsonBtn = document.getElementById('exportJson');
        
        if (exportTxtBtn) {
            exportTxtBtn.addEventListener('click', () => this.exportResults('txt'));
        }
        
        if (exportJsonBtn) {
            exportJsonBtn.addEventListener('click', () => this.exportResults('json'));
        }

        // File upload
        const fileInput = document.getElementById('fileInput');
        if (fileInput) {
            fileInput.addEventListener('change', (e) => this.handleFileUpload(e));
        }
    }

    initTooltips() {
        // Initialize Bootstrap tooltips if available
        if (typeof bootstrap !== 'undefined') {
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }
    }

    initAutoSave() {
        // Auto-save form data to localStorage
        const inputs = document.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            // Load saved value
            const savedValue = localStorage.getItem(`textSearchReplace_${input.name}`);
            if (savedValue && input.type !== 'file') {
                if (input.type === 'checkbox') {
                    input.checked = savedValue === 'true';
                } else {
                    input.value = savedValue;
                }
            }

            // Save on change
            input.addEventListener('input', () => {
                if (input.type === 'checkbox') {
                    localStorage.setItem(`textSearchReplace_${input.name}`, input.checked);
                } else {
                    localStorage.setItem(`textSearchReplace_${input.name}`, input.value);
                }
            });
        });
    }

    initKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl+Enter to submit form
            if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
                e.preventDefault();
                const form = document.getElementById('searchForm');
                if (form) {
                    form.submit();
                }
            }

            // Ctrl+K to focus search input
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                const searchInput = document.getElementById('search');
                if (searchInput) {
                    searchInput.focus();
                    searchInput.select();
                }
            }

            // Escape to clear form
            if (e.key === 'Escape') {
                this.clearForm();
            }
        });
    }

    initRealTimeStats() {
        this.updateRealTimeStats();
    }

    initFormValidation() {
        const form = document.getElementById('searchForm');
        if (!form) return;

        const inputs = form.querySelectorAll('input, textarea');
        inputs.forEach(input => {
            input.addEventListener('blur', () => this.validateField(input));
            input.addEventListener('input', () => this.clearFieldError(input));
        });
    }

    validateField(field) {
        const value = field.value.trim();
        let isValid = true;
        let errorMessage = '';

        switch (field.name) {
            case 'search':
                if (!value) {
                    isValid = false;
                    errorMessage = 'Search term is required';
                } else if (value.length > 1000) {
                    isValid = false;
                    errorMessage = 'Search term is too long (max 1000 characters)';
                }
                break;

            case 'article':
                if (!value) {
                    isValid = false;
                    errorMessage = 'Text content is required';
                } else if (value.length > 100000) {
                    isValid = false;
                    errorMessage = 'Text is too long (max 100,000 characters)';
                }
                break;

            case 'replace':
                if (value.length > 1000) {
                    isValid = false;
                    errorMessage = 'Replace term is too long (max 1000 characters)';
                }
                break;
        }

        this.setFieldValidation(field, isValid, errorMessage);
        return isValid;
    }

    setFieldValidation(field, isValid, errorMessage) {
        field.classList.remove('is-valid', 'is-invalid');
        
        // Remove existing error message
        const existingError = field.parentNode.querySelector('.invalid-feedback');
        if (existingError) {
            existingError.remove();
        }

        if (isValid) {
            field.classList.add('is-valid');
        } else {
            field.classList.add('is-invalid');
            
            // Add error message
            const errorDiv = document.createElement('div');
            errorDiv.className = 'invalid-feedback';
            errorDiv.textContent = errorMessage;
            field.parentNode.appendChild(errorDiv);
        }
    }

    clearFieldError(field) {
        field.classList.remove('is-invalid');
        const errorDiv = field.parentNode.querySelector('.invalid-feedback');
        if (errorDiv) {
            errorDiv.remove();
        }
    }

    updateRealTimeStats() {
        const articleInput = document.getElementById('article');
        const searchInput = document.getElementById('search');
        const statsContainer = document.getElementById('realTimeStats');

        if (!articleInput || !searchInput || !statsContainer) return;

        const text = articleInput.value;
        const searchTerm = searchInput.value;

        if (!text) {
            statsContainer.innerHTML = '<p class="text-muted">Enter some text to see statistics</p>';
            return;
        }

        const stats = this.calculateStats(text, searchTerm);
        this.displayStats(stats, statsContainer);
    }

    calculateStats(text, searchTerm) {
        const words = text.trim() ? text.trim().split(/\s+/).length : 0;
        const characters = text.length;
        const charactersNoSpaces = text.replace(/\s/g, '').length;
        const lines = text.split('\n').length;
        const paragraphs = text.split(/\n\s*\n/).length;

        let occurrences = 0;
        let percentage = 0;

        if (searchTerm && text) {
            const regex = new RegExp(searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'gi');
            const matches = text.match(regex);
            occurrences = matches ? matches.length : 0;
            percentage = words > 0 ? ((occurrences / words) * 100).toFixed(2) : 0;
        }

        return {
            words,
            characters,
            charactersNoSpaces,
            lines,
            paragraphs,
            occurrences,
            percentage
        };
    }

    displayStats(stats, container) {
        container.innerHTML = `
            <div class="stats-grid">
                <div class="stat-item">
                    <span class="stat-number">${stats.words.toLocaleString()}</span>
                    <span class="stat-label">Words</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">${stats.characters.toLocaleString()}</span>
                    <span class="stat-label">Characters</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">${stats.charactersNoSpaces.toLocaleString()}</span>
                    <span class="stat-label">Characters (no spaces)</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">${stats.lines.toLocaleString()}</span>
                    <span class="stat-label">Lines</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">${stats.paragraphs.toLocaleString()}</span>
                    <span class="stat-label">Paragraphs</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">${stats.occurrences.toLocaleString()}</span>
                    <span class="stat-label">Matches Found</span>
                </div>
            </div>
        `;
    }

    handleFormSubmit(e) {
        e.preventDefault();
        
        // Validate all fields
        const form = e.target;
        const inputs = form.querySelectorAll('input[required], textarea[required]');
        let isFormValid = true;

        inputs.forEach(input => {
            if (!this.validateField(input)) {
                isFormValid = false;
            }
        });

        if (!isFormValid) {
            this.showAlert('Please fix the errors before submitting', 'danger');
            return;
        }

        // Show loading state
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;
        submitBtn.innerHTML = '<span class="loading"></span> Processing...';
        submitBtn.disabled = true;

        // Submit form
        setTimeout(() => {
            form.submit();
        }, 500);
    }

    clearForm() {
        const form = document.getElementById('searchForm');
        if (form) {
            form.reset();
            
            // Clear localStorage
            const inputs = form.querySelectorAll('input, textarea, select');
            inputs.forEach(input => {
                localStorage.removeItem(`textSearchReplace_${input.name}`);
                this.clearFieldError(input);
            });
            
            // Clear real-time stats
            this.updateRealTimeStats();
            
            this.showAlert('Form cleared successfully', 'success');
        }
    }

    handleFileUpload(e) {
        const file = e.target.files[0];
        if (!file) return;

        // Check file size (max 1MB)
        if (file.size > 1024 * 1024) {
            this.showAlert('File is too large. Maximum size is 1MB.', 'danger');
            e.target.value = '';
            return;
        }

        // Check file type
        const allowedTypes = ['text/plain', 'text/html', 'text/css', 'text/javascript', 'application/json'];
        if (!allowedTypes.includes(file.type) && !file.name.match(/\.(txt|html|css|js|json|md)$/i)) {
            this.showAlert('Invalid file type. Please upload a text file.', 'danger');
            e.target.value = '';
            return;
        }

        const reader = new FileReader();
        reader.onload = (event) => {
            const articleInput = document.getElementById('article');
            if (articleInput) {
                articleInput.value = event.target.result;
                this.updateRealTimeStats();
                this.showAlert('File uploaded successfully', 'success');
            }
        };

        reader.onerror = () => {
            this.showAlert('Error reading file', 'danger');
        };

        reader.readAsText(file);
    }

    exportResults(format) {
        const resultsContent = document.querySelector('.results-content');
        if (!resultsContent) {
            this.showAlert('No results to export', 'warning');
            return;
        }

        const content = resultsContent.textContent || resultsContent.innerText;
        const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');

        if (format === 'txt') {
            this.downloadFile(content, `search-results-${timestamp}.txt`, 'text/plain');
        } else if (format === 'json') {
            const data = {
                timestamp: new Date().toISOString(),
                search_term: document.getElementById('search')?.value || '',
                replace_term: document.getElementById('replace')?.value || '',
                original_text: document.getElementById('article')?.value || '',
                results: content,
                statistics: this.getStatisticsData()
            };
            
            this.downloadFile(
                JSON.stringify(data, null, 2),
                `search-results-${timestamp}.json`,
                'application/json'
            );
        }
    }

    downloadFile(content, filename, mimeType) {
        const blob = new Blob([content], { type: mimeType });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        this.showAlert(`File "${filename}" downloaded successfully`, 'success');
    }

    getStatisticsData() {
        const statsElements = document.querySelectorAll('.stat-item');
        const stats = {};
        
        statsElements.forEach(element => {
            const number = element.querySelector('.stat-number')?.textContent || '0';
            const label = element.querySelector('.stat-label')?.textContent || '';
            if (label) {
                stats[label.toLowerCase().replace(/\s+/g, '_')] = parseInt(number.replace(/,/g, ''));
            }
        });
        
        return stats;
    }

    saveSettings() {
        const settings = {};
        const optionInputs = document.querySelectorAll('.option-input');
        
        optionInputs.forEach(input => {
            if (input.type === 'checkbox') {
                settings[input.name] = input.checked;
            } else {
                settings[input.name] = input.value;
            }
        });
        
        localStorage.setItem('textSearchReplace_settings', JSON.stringify(settings));
    }

    loadSettings() {
        const savedSettings = localStorage.getItem('textSearchReplace_settings');
        if (!savedSettings) return;
        
        try {
            const settings = JSON.parse(savedSettings);
            
            Object.keys(settings).forEach(key => {
                const input = document.querySelector(`[name="${key}"]`);
                if (input) {
                    if (input.type === 'checkbox') {
                        input.checked = settings[key];
                    } else {
                        input.value = settings[key];
                    }
                }
            });
        } catch (e) {
            console.error('Error loading settings:', e);
        }
    }

    showAlert(message, type = 'info') {
        // Remove existing alerts
        const existingAlerts = document.querySelectorAll('.dynamic-alert');
        existingAlerts.forEach(alert => alert.remove());

        // Create new alert
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show dynamic-alert`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        // Insert at the top of content
        const content = document.querySelector('.content');
        if (content) {
            content.insertBefore(alertDiv, content.firstChild);
        }

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new TextSearchReplace();
});

// Add some utility functions to global scope
window.TextSearchReplaceUtils = {
    copyToClipboard: function(text) {
        if (navigator.clipboard) {
            navigator.clipboard.writeText(text).then(() => {
                console.log('Text copied to clipboard');
            });
        } else {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
        }
    },

    formatNumber: function(num) {
        return num.toLocaleString();
    },

    truncateText: function(text, maxLength = 100) {
        if (text.length <= maxLength) return text;
        return text.substring(0, maxLength) + '...';
    }
};
