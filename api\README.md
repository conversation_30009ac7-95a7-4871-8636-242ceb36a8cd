# Text Search & Replace Tool - API Documentation

## Overview

This API provides programmatic access to the Text Search & Replace Tool functionality. It supports text processing, search, replace, and statistical analysis operations.

## Base URL

```
http://your-domain.com/api/
```

## Authentication

Currently, the API is open and doesn't require authentication. Rate limiting is applied based on IP address.

## Rate Limiting

- **50 requests per hour** per IP address for processing endpoints
- **100 requests per hour** per IP address for system endpoints

## Content Type

All requests should use `Content-Type: application/json` for POST requests.

## Response Format

All responses follow this format:

```json
{
    "success": true|false,
    "message": "Response message",
    "data": {...},
    "timestamp": "2023-12-07T10:30:00+00:00"
}
```

## Endpoints

### 1. Text Search & Replace

**POST** `/search`

Process text with search and optional replace functionality.

#### Request Body

```json
{
    "text": "Your text content here",
    "search": "search term",
    "replace": "replacement text (optional)",
    "case_sensitive": false,
    "use_regex": false
}
```

#### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| text | string | Yes | The text to process (max 100,000 characters) |
| search | string | Yes | The search term or pattern |
| replace | string | No | Replacement text (if omitted, only search is performed) |
| case_sensitive | boolean | No | Whether search is case sensitive (default: false) |
| use_regex | boolean | No | Whether to use regular expressions (default: false) |

#### Response

```json
{
    "success": true,
    "message": "Text processed successfully",
    "data": {
        "statistics": {
            "word_count": 150,
            "character_count": 850,
            "character_count_no_spaces": 720,
            "paragraph_count": 3,
            "line_count": 8,
            "sentence_count": 12
        },
        "occurrences": 5,
        "percentage": 3.33,
        "positions": [
            {
                "position": 45,
                "line": 2,
                "context": {
                    "before": "text before match",
                    "match": "search term",
                    "after": "text after match"
                }
            }
        ],
        "highlighted_text": "Text with <mark class=\"highlight\">search term</mark> highlighted",
        "replaced_text": "Text with replacement text substituted"
    },
    "timestamp": "2023-12-07T10:30:00+00:00"
}
```

#### Example

```bash
curl -X POST http://your-domain.com/api/search \
  -H "Content-Type: application/json" \
  -d '{
    "text": "This is a sample text. This text contains multiple words.",
    "search": "text",
    "replace": "document",
    "case_sensitive": false
  }'
```

### 2. Text Statistics

**POST** `/statistics`

Get detailed statistics about a text without performing search operations.

#### Request Body

```json
{
    "text": "Your text content here"
}
```

#### Response

```json
{
    "success": true,
    "message": "Statistics calculated successfully",
    "data": {
        "word_count": 150,
        "character_count": 850,
        "character_count_no_spaces": 720,
        "paragraph_count": 3,
        "line_count": 8,
        "sentence_count": 12
    },
    "timestamp": "2023-12-07T10:30:00+00:00"
}
```

### 3. System Status

**GET** `/status`

Get system information and status.

#### Response

```json
{
    "success": true,
    "message": "System status retrieved",
    "data": {
        "app_name": "Text Search & Replace Tool",
        "version": "2.0.0",
        "php_version": "8.1.0",
        "cache_enabled": true,
        "debug_mode": false,
        "timestamp": "2023-12-07T10:30:00+00:00",
        "uptime": [0.5, 0.3, 0.2],
        "memory_usage": {
            "current": 2097152,
            "peak": 4194304
        }
    },
    "timestamp": "2023-12-07T10:30:00+00:00"
}
```

### 4. Health Check

**GET** `/health`

Check system health and dependencies.

#### Response

```json
{
    "success": true,
    "message": "Health check completed",
    "data": {
        "status": "healthy",
        "checks": {
            "php": true,
            "mbstring": true,
            "json": true,
            "session": true,
            "cache_dir": true,
            "logs_dir": true
        }
    },
    "timestamp": "2023-12-07T10:30:00+00:00"
}
```

## Error Responses

### 400 Bad Request

```json
{
    "success": false,
    "message": "Text and search term are required",
    "timestamp": "2023-12-07T10:30:00+00:00"
}
```

### 404 Not Found

```json
{
    "success": false,
    "message": "Endpoint not found",
    "timestamp": "2023-12-07T10:30:00+00:00"
}
```

### 429 Too Many Requests

```json
{
    "success": false,
    "message": "Rate limit exceeded",
    "timestamp": "2023-12-07T10:30:00+00:00"
}
```

### 500 Internal Server Error

```json
{
    "success": false,
    "message": "Processing failed: Error details",
    "timestamp": "2023-12-07T10:30:00+00:00"
}
```

## Regular Expressions

When `use_regex` is set to `true`, the search term is treated as a regular expression pattern. 

### Supported Patterns

- `\d+` - Match digits
- `\w+` - Match word characters
- `^text` - Match text at line start
- `text$` - Match text at line end
- `[a-z]+` - Match lowercase letters
- `(pattern1|pattern2)` - Match either pattern

### Security

For security reasons, certain regex patterns are blocked:
- Code execution patterns
- Potentially dangerous constructs
- Patterns that could cause excessive processing

## Code Examples

### JavaScript (Fetch API)

```javascript
async function searchText(text, searchTerm, replaceTerm = '') {
    const response = await fetch('/api/search', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            text: text,
            search: searchTerm,
            replace: replaceTerm,
            case_sensitive: false,
            use_regex: false
        })
    });
    
    const result = await response.json();
    return result;
}

// Usage
searchText('Hello world', 'world', 'universe')
    .then(result => console.log(result))
    .catch(error => console.error(error));
```

### Python (requests)

```python
import requests
import json

def search_text(text, search_term, replace_term=''):
    url = 'http://your-domain.com/api/search'
    data = {
        'text': text,
        'search': search_term,
        'replace': replace_term,
        'case_sensitive': False,
        'use_regex': False
    }
    
    response = requests.post(url, json=data)
    return response.json()

# Usage
result = search_text('Hello world', 'world', 'universe')
print(json.dumps(result, indent=2))
```

### PHP (cURL)

```php
function searchText($text, $searchTerm, $replaceTerm = '') {
    $url = 'http://your-domain.com/api/search';
    $data = [
        'text' => $text,
        'search' => $searchTerm,
        'replace' => $replaceTerm,
        'case_sensitive' => false,
        'use_regex' => false
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    
    $response = curl_exec($ch);
    curl_close($ch);
    
    return json_decode($response, true);
}

// Usage
$result = searchText('Hello world', 'world', 'universe');
print_r($result);
```

## Limitations

- Maximum text length: 100,000 characters
- Rate limiting applies
- Regex patterns are validated for security
- No authentication required (currently)

## Future Enhancements

- User authentication and API keys
- Batch processing endpoints
- File upload support
- Webhook notifications
- Advanced analytics endpoints

## Support

For API support and questions:
- Check the main application documentation
- Review error messages for specific issues
- Monitor rate limiting headers
- Ensure proper JSON formatting
