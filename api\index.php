<?php
/**
 * REST API for Text Search & Replace Tool
 * Future feature for programmatic access
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once '../autoload.php';

/**
 * API Response Helper
 */
class ApiResponse
{
    public static function success($data = null, $message = 'Success', $code = 200)
    {
        http_response_code($code);
        echo json_encode([
            'success' => true,
            'message' => $message,
            'data' => $data,
            'timestamp' => date('c')
        ], JSON_UNESCAPED_UNICODE);
        exit();
    }
    
    public static function error($message = 'Error', $code = 400, $details = null)
    {
        http_response_code($code);
        echo json_encode([
            'success' => false,
            'message' => $message,
            'details' => $details,
            'timestamp' => date('c')
        ], JSON_UNESCAPED_UNICODE);
        exit();
    }
}

/**
 * API Router
 */
class ApiRouter
{
    private $routes = [];
    
    public function addRoute($method, $path, $handler)
    {
        $this->routes[] = [
            'method' => strtoupper($method),
            'path' => $path,
            'handler' => $handler
        ];
    }
    
    public function handleRequest()
    {
        $method = $_SERVER['REQUEST_METHOD'];
        $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
        $path = str_replace('/api', '', $path); // Remove /api prefix
        
        foreach ($this->routes as $route) {
            if ($route['method'] === $method && $this->matchPath($route['path'], $path)) {
                $params = $this->extractParams($route['path'], $path);
                call_user_func($route['handler'], $params);
                return;
            }
        }
        
        ApiResponse::error('Endpoint not found', 404);
    }
    
    private function matchPath($routePath, $requestPath)
    {
        $routePattern = preg_replace('/\{[^}]+\}/', '([^/]+)', $routePath);
        $routePattern = '#^' . $routePattern . '$#';
        return preg_match($routePattern, $requestPath);
    }
    
    private function extractParams($routePath, $requestPath)
    {
        $routePattern = preg_replace('/\{([^}]+)\}/', '(?P<$1>[^/]+)', $routePath);
        $routePattern = '#^' . $routePattern . '$#';
        
        if (preg_match($routePattern, $requestPath, $matches)) {
            return array_filter($matches, 'is_string', ARRAY_FILTER_USE_KEY);
        }
        
        return [];
    }
}

/**
 * API Controllers
 */
class TextProcessorController
{
    public static function search($params = [])
    {
        try {
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input) {
                ApiResponse::error('Invalid JSON input', 400);
            }
            
            // Validate required fields
            if (empty($input['text']) || empty($input['search'])) {
                ApiResponse::error('Text and search term are required', 400);
            }
            
            // Rate limiting
            if (!SecurityManager::checkRateLimit($_SERVER['REMOTE_ADDR'] ?? 'unknown', 50, 3600)) {
                ApiResponse::error('Rate limit exceeded', 429);
            }
            
            // Sanitize inputs
            $text = SecurityManager::sanitizeInput($input['text']);
            $search = SecurityManager::sanitizeInput($input['search']);
            $replace = SecurityManager::sanitizeInput($input['replace'] ?? '');
            $caseSensitive = $input['case_sensitive'] ?? false;
            $useRegex = $input['use_regex'] ?? false;
            
            // Validate text length
            if (!SecurityManager::validateTextLength($text)) {
                ApiResponse::error('Text too long. Maximum length is ' . MAX_TEXT_LENGTH . ' characters', 400);
            }
            
            // Validate regex if used
            if ($useRegex && !SecurityManager::validateRegexPattern($search)) {
                ApiResponse::error('Invalid regular expression pattern', 400);
            }
            
            // Process text
            $processor = new TextProcessor($text, $caseSensitive, $useRegex);
            $processor->setSearchTerm($search)->setReplaceTerm($replace);
            
            $results = [
                'statistics' => $processor->getStatistics(),
                'occurrences' => $processor->countOccurrences(),
                'percentage' => $processor->getPercentage(),
                'positions' => $processor->findPositions(),
                'highlighted_text' => $processor->highlightText(),
                'replaced_text' => !empty($replace) ? $processor->replaceText() : null
            ];
            
            ApiResponse::success($results, 'Text processed successfully');
            
        } catch (Exception $e) {
            SecurityManager::logSecurityEvent('api_error', ['error' => $e->getMessage()]);
            ApiResponse::error('Processing failed: ' . $e->getMessage(), 500);
        }
    }
    
    public static function statistics($params = [])
    {
        try {
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input || empty($input['text'])) {
                ApiResponse::error('Text is required', 400);
            }
            
            $text = SecurityManager::sanitizeInput($input['text']);
            
            if (!SecurityManager::validateTextLength($text)) {
                ApiResponse::error('Text too long', 400);
            }
            
            $processor = new TextProcessor();
            $stats = $processor->getStatistics($text);
            
            ApiResponse::success($stats, 'Statistics calculated successfully');
            
        } catch (Exception $e) {
            ApiResponse::error('Statistics calculation failed: ' . $e->getMessage(), 500);
        }
    }
}

class SystemController
{
    public static function status($params = [])
    {
        $status = [
            'app_name' => APP_NAME,
            'version' => APP_VERSION,
            'php_version' => PHP_VERSION,
            'cache_enabled' => CACHE_ENABLED,
            'debug_mode' => DEBUG_MODE,
            'timestamp' => date('c'),
            'uptime' => sys_getloadavg(),
            'memory_usage' => [
                'current' => memory_get_usage(true),
                'peak' => memory_get_peak_usage(true)
            ]
        ];
        
        ApiResponse::success($status, 'System status retrieved');
    }
    
    public static function health($params = [])
    {
        $health = [
            'status' => 'healthy',
            'checks' => [
                'php' => version_compare(PHP_VERSION, '7.4.0', '>='),
                'mbstring' => extension_loaded('mbstring'),
                'json' => extension_loaded('json'),
                'session' => extension_loaded('session'),
                'cache_dir' => is_writable('cache'),
                'logs_dir' => is_writable('logs')
            ]
        ];
        
        $allHealthy = array_reduce($health['checks'], function($carry, $check) {
            return $carry && $check;
        }, true);
        
        if (!$allHealthy) {
            $health['status'] = 'unhealthy';
        }
        
        ApiResponse::success($health, 'Health check completed');
    }
}

// Initialize router and define routes
$router = new ApiRouter();

// Text processing endpoints
$router->addRoute('POST', '/search', [TextProcessorController::class, 'search']);
$router->addRoute('POST', '/statistics', [TextProcessorController::class, 'statistics']);

// System endpoints
$router->addRoute('GET', '/status', [SystemController::class, 'status']);
$router->addRoute('GET', '/health', [SystemController::class, 'health']);

// Handle the request
try {
    $router->handleRequest();
} catch (Exception $e) {
    ApiResponse::error('Internal server error', 500, DEBUG_MODE ? $e->getMessage() : null);
}
