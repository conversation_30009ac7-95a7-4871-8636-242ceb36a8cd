<?php
/**
 * CacheManager Class - Simple file-based caching system
 */

class CacheManager
{
    private static $cacheDir = 'cache/';
    
    /**
     * Initialize cache directory
     */
    public static function init()
    {
        if (!is_dir(self::$cacheDir)) {
            mkdir(self::$cacheDir, 0755, true);
        }
    }
    
    /**
     * Generate cache key
     */
    private static function generateKey($data)
    {
        return md5(serialize($data));
    }
    
    /**
     * Get cache file path
     */
    private static function getCacheFilePath($key)
    {
        return self::$cacheDir . $key . '.cache';
    }
    
    /**
     * Store data in cache
     */
    public static function set($key, $data, $duration = CACHE_DURATION)
    {
        if (!CACHE_ENABLED) {
            return false;
        }
        
        self::init();
        
        $cacheKey = self::generateKey($key);
        $filePath = self::getCacheFilePath($cacheKey);
        
        $cacheData = [
            'data' => $data,
            'expires' => time() + $duration,
            'created' => time()
        ];
        
        return file_put_contents($filePath, serialize($cacheData)) !== false;
    }
    
    /**
     * Get data from cache
     */
    public static function get($key)
    {
        if (!CACHE_ENABLED) {
            return null;
        }
        
        $cacheKey = self::generateKey($key);
        $filePath = self::getCacheFilePath($cacheKey);
        
        if (!file_exists($filePath)) {
            return null;
        }
        
        $cacheData = unserialize(file_get_contents($filePath));
        
        if (!$cacheData || $cacheData['expires'] < time()) {
            self::delete($key);
            return null;
        }
        
        return $cacheData['data'];
    }
    
    /**
     * Delete cache entry
     */
    public static function delete($key)
    {
        $cacheKey = self::generateKey($key);
        $filePath = self::getCacheFilePath($cacheKey);
        
        if (file_exists($filePath)) {
            return unlink($filePath);
        }
        
        return true;
    }
    
    /**
     * Clear all cache
     */
    public static function clear()
    {
        if (!is_dir(self::$cacheDir)) {
            return true;
        }
        
        $files = glob(self::$cacheDir . '*.cache');
        foreach ($files as $file) {
            unlink($file);
        }
        
        return true;
    }
    
    /**
     * Clean expired cache entries
     */
    public static function cleanExpired()
    {
        if (!is_dir(self::$cacheDir)) {
            return true;
        }
        
        $files = glob(self::$cacheDir . '*.cache');
        $cleaned = 0;
        
        foreach ($files as $file) {
            $cacheData = unserialize(file_get_contents($file));
            if (!$cacheData || $cacheData['expires'] < time()) {
                unlink($file);
                $cleaned++;
            }
        }
        
        return $cleaned;
    }
    
    /**
     * Get cache statistics
     */
    public static function getStats()
    {
        if (!is_dir(self::$cacheDir)) {
            return [
                'total_files' => 0,
                'total_size' => 0,
                'expired_files' => 0
            ];
        }
        
        $files = glob(self::$cacheDir . '*.cache');
        $totalSize = 0;
        $expiredFiles = 0;
        
        foreach ($files as $file) {
            $totalSize += filesize($file);
            
            $cacheData = unserialize(file_get_contents($file));
            if (!$cacheData || $cacheData['expires'] < time()) {
                $expiredFiles++;
            }
        }
        
        return [
            'total_files' => count($files),
            'total_size' => $totalSize,
            'expired_files' => $expiredFiles,
            'cache_dir' => self::$cacheDir
        ];
    }
}
