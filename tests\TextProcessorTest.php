<?php
/**
 * Unit Tests for TextProcessor Class
 */

require_once '../autoload.php';

class TextProcessorTest
{
    private $processor;
    private $testText = "This is a sample text. This text contains multiple words. Text processing is important.";
    
    public function __construct()
    {
        $this->processor = new TextProcessor();
        echo "<h2>Running TextProcessor Tests</h2>\n";
    }
    
    public function runAllTests()
    {
        $this->testWordCount();
        $this->testCharacterCount();
        $this->testCountOccurrences();
        $this->testPercentage();
        $this->testHighlightText();
        $this->testReplaceText();
        $this->testStatistics();
        $this->testFindPositions();
        $this->testCaseSensitive();
        
        echo "<h3>All tests completed!</h3>\n";
    }
    
    private function testWordCount()
    {
        echo "<h4>Testing Word Count</h4>\n";
        
        $count = $this->processor->getWordCount($this->testText);
        $expected = 13; // Expected word count
        
        $this->assert($count === $expected, "Word count test", "Expected: $expected, Got: $count");
    }
    
    private function testCharacterCount()
    {
        echo "<h4>Testing Character Count</h4>\n";
        
        $count = $this->processor->getCharacterCount($this->testText);
        $expected = strlen($this->testText);
        
        $this->assert($count === $expected, "Character count test", "Expected: $expected, Got: $count");
    }
    
    private function testCountOccurrences()
    {
        echo "<h4>Testing Count Occurrences</h4>\n";
        
        $this->processor->setText($this->testText)->setSearchTerm("text");
        $count = $this->processor->countOccurrences();
        $expected = 3; // "text" appears 3 times (case insensitive)
        
        $this->assert($count === $expected, "Count occurrences test", "Expected: $expected, Got: $count");
    }
    
    private function testPercentage()
    {
        echo "<h4>Testing Percentage</h4>\n";
        
        $this->processor->setText($this->testText)->setSearchTerm("text");
        $percentage = $this->processor->getPercentage();
        $expected = round((3 / 13) * 100, 2); // 3 occurrences out of 13 words
        
        $this->assert($percentage === $expected, "Percentage test", "Expected: $expected, Got: $percentage");
    }
    
    private function testHighlightText()
    {
        echo "<h4>Testing Highlight Text</h4>\n";
        
        $this->processor->setText("Hello world")->setSearchTerm("world");
        $highlighted = $this->processor->highlightText();
        $expected = 'Hello <mark class="highlight">world</mark>';
        
        $this->assert(strpos($highlighted, '<mark') !== false, "Highlight text test", "Should contain highlight markup");
    }
    
    private function testReplaceText()
    {
        echo "<h4>Testing Replace Text</h4>\n";
        
        $this->processor->setText("Hello world")->setSearchTerm("world")->setReplaceTerm("PHP");
        $replaced = $this->processor->replaceText();
        $expected = "Hello PHP";
        
        $this->assert($replaced === $expected, "Replace text test", "Expected: '$expected', Got: '$replaced'");
    }
    
    private function testStatistics()
    {
        echo "<h4>Testing Statistics</h4>\n";
        
        $stats = $this->processor->getStatistics($this->testText);
        
        $this->assert(isset($stats['word_count']), "Statistics test", "Should contain word_count");
        $this->assert(isset($stats['character_count']), "Statistics test", "Should contain character_count");
        $this->assert(isset($stats['line_count']), "Statistics test", "Should contain line_count");
        $this->assert($stats['word_count'] > 0, "Statistics test", "Word count should be greater than 0");
    }
    
    private function testFindPositions()
    {
        echo "<h4>Testing Find Positions</h4>\n";
        
        $this->processor->setText($this->testText)->setSearchTerm("text");
        $positions = $this->processor->findPositions();
        
        $this->assert(is_array($positions), "Find positions test", "Should return an array");
        $this->assert(count($positions) === 3, "Find positions test", "Should find 3 positions");
        $this->assert(isset($positions[0]['position']), "Find positions test", "Should contain position data");
    }
    
    private function testCaseSensitive()
    {
        echo "<h4>Testing Case Sensitive</h4>\n";
        
        $processor = new TextProcessor("Hello WORLD", true); // Case sensitive
        $processor->setSearchTerm("world");
        $count = $processor->countOccurrences();
        
        $this->assert($count === 0, "Case sensitive test", "Should not find 'world' in 'WORLD' when case sensitive");
        
        $processor = new TextProcessor("Hello WORLD", false); // Case insensitive
        $processor->setSearchTerm("world");
        $count = $processor->countOccurrences();
        
        $this->assert($count === 1, "Case insensitive test", "Should find 'world' in 'WORLD' when case insensitive");
    }
    
    private function assert($condition, $testName, $message = "")
    {
        if ($condition) {
            echo "<p style='color: green;'>✓ $testName: PASSED</p>\n";
        } else {
            echo "<p style='color: red;'>✗ $testName: FAILED - $message</p>\n";
        }
    }
}

// Run tests if accessed directly
if (basename($_SERVER['PHP_SELF']) === 'TextProcessorTest.php') {
    echo "<!DOCTYPE html><html><head><title>TextProcessor Tests</title></head><body>";
    $test = new TextProcessorTest();
    $test->runAllTests();
    echo "</body></html>";
}
