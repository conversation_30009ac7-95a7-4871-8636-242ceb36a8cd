<?php
/**
 * Security Tests for the application
 */

require_once '../autoload.php';

class SecurityTest
{
    public function __construct()
    {
        echo "<h2>Running Security Tests</h2>\n";
    }
    
    public function runAllTests()
    {
        $this->testCSRFToken();
        $this->testInputSanitization();
        $this->testTextLengthValidation();
        $this->testRegexValidation();
        $this->testRateLimit();
        
        echo "<h3>All security tests completed!</h3>\n";
    }
    
    private function testCSRFToken()
    {
        echo "<h4>Testing CSRF Token</h4>\n";
        
        // Test token generation
        $token1 = SecurityManager::generateCSRFToken();
        $token2 = SecurityManager::generateCSRFToken();
        
        $this->assert(!empty($token1), "CSRF token generation", "Token should not be empty");
        $this->assert($token1 === $token2, "CSRF token consistency", "Same session should return same token");
        
        // Test token verification
        $isValid = SecurityManager::verifyCSRFToken($token1);
        $this->assert($isValid, "CSRF token verification", "Valid token should pass verification");
        
        $isInvalid = SecurityManager::verifyCSRFToken("invalid_token");
        $this->assert(!$isInvalid, "CSRF token verification", "Invalid token should fail verification");
    }
    
    private function testInputSanitization()
    {
        echo "<h4>Testing Input Sanitization</h4>\n";
        
        // Test XSS prevention
        $maliciousInput = "<script>alert('XSS')</script>";
        $sanitized = SecurityManager::sanitizeInput($maliciousInput);
        
        $this->assert(strpos($sanitized, '<script>') === false, "XSS prevention", "Script tags should be escaped");
        
        // Test HTML entities
        $htmlInput = "<b>Bold text</b>";
        $sanitized = SecurityManager::sanitizeInput($htmlInput);
        
        $this->assert(strpos($sanitized, '&lt;') !== false, "HTML escaping", "HTML should be escaped");
        
        // Test array sanitization
        $arrayInput = ["<script>", "normal text", "<img src=x>"];
        $sanitized = SecurityManager::sanitizeInput($arrayInput);
        
        $this->assert(is_array($sanitized), "Array sanitization", "Should return array");
        $this->assert(strpos($sanitized[0], '<script>') === false, "Array XSS prevention", "Array elements should be sanitized");
    }
    
    private function testTextLengthValidation()
    {
        echo "<h4>Testing Text Length Validation</h4>\n";
        
        // Test normal length
        $normalText = str_repeat("a", 1000);
        $isValid = SecurityManager::validateTextLength($normalText);
        
        $this->assert($isValid, "Normal text length", "Normal length text should be valid");
        
        // Test excessive length
        $longText = str_repeat("a", MAX_TEXT_LENGTH + 1);
        $isInvalid = SecurityManager::validateTextLength($longText);
        
        $this->assert(!$isInvalid, "Excessive text length", "Too long text should be invalid");
    }
    
    private function testRegexValidation()
    {
        echo "<h4>Testing Regex Validation</h4>\n";
        
        // Test valid regex
        $validRegex = "/test/i";
        $isValid = SecurityManager::validateRegexPattern($validRegex);
        
        $this->assert($isValid, "Valid regex", "Valid regex should pass validation");
        
        // Test invalid regex
        $invalidRegex = "/test(/";
        $isInvalid = SecurityManager::validateRegexPattern($invalidRegex);
        
        $this->assert(!$isInvalid, "Invalid regex", "Invalid regex should fail validation");
        
        // Test potentially dangerous regex
        $dangerousRegex = "/(?{echo 'dangerous';})/";
        $isDangerous = SecurityManager::validateRegexPattern($dangerousRegex);
        
        $this->assert(!$isDangerous, "Dangerous regex", "Dangerous regex should be blocked");
    }
    
    private function testRateLimit()
    {
        echo "<h4>Testing Rate Limiting</h4>\n";
        
        $identifier = "test_user_" . time();
        
        // Test normal usage
        $allowed = SecurityManager::checkRateLimit($identifier, 5, 60); // 5 requests per minute
        $this->assert($allowed, "Rate limit - first request", "First request should be allowed");
        
        // Test multiple requests
        for ($i = 0; $i < 4; $i++) {
            SecurityManager::checkRateLimit($identifier, 5, 60);
        }
        
        // This should be the 6th request and should be blocked
        $blocked = SecurityManager::checkRateLimit($identifier, 5, 60);
        $this->assert(!$blocked, "Rate limit - exceeded", "Request should be blocked after limit exceeded");
    }
    
    private function assert($condition, $testName, $message = "")
    {
        if ($condition) {
            echo "<p style='color: green;'>✓ $testName: PASSED</p>\n";
        } else {
            echo "<p style='color: red;'>✗ $testName: FAILED - $message</p>\n";
        }
    }
}

// Run tests if accessed directly
if (basename($_SERVER['PHP_SELF']) === 'SecurityTest.php') {
    echo "<!DOCTYPE html><html><head><title>Security Tests</title></head><body>";
    $test = new SecurityTest();
    $test->runAllTests();
    echo "</body></html>";
}
