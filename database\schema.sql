-- Text Search & Replace Tool Database Schema
-- For future features like user management, search history, etc.

-- Users table (for future user management)
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP NULL,
    preferences JSON NULL
);

-- Search history table
CREATE TABLE IF NOT EXISTS search_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NULL,
    search_term VARCHAR(1000) NOT NULL,
    replace_term VARCHAR(1000) NULL,
    text_length INT NOT NULL,
    occurrences_found INT NOT NULL,
    case_sensitive BOOLEAN DEFAULT FALSE,
    use_regex BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_created_at (created_at),
    INDEX idx_search_term (search_term(100))
);

-- Saved searches table
CREATE TABLE IF NOT EXISTS saved_searches (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    name VARCHAR(100) NOT NULL,
    search_term VARCHAR(1000) NOT NULL,
    replace_term VARCHAR(1000) NULL,
    case_sensitive BOOLEAN DEFAULT FALSE,
    use_regex BOOLEAN DEFAULT FALSE,
    description TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_name (name)
);

-- Text templates table
CREATE TABLE IF NOT EXISTS text_templates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NULL,
    name VARCHAR(100) NOT NULL,
    content TEXT NOT NULL,
    description TEXT NULL,
    is_public BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_is_public (is_public),
    INDEX idx_name (name)
);

-- Analytics table
CREATE TABLE IF NOT EXISTS analytics (
    id INT AUTO_INCREMENT PRIMARY KEY,
    event_type VARCHAR(50) NOT NULL,
    event_data JSON NULL,
    user_id INT NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_event_type (event_type),
    INDEX idx_created_at (created_at),
    INDEX idx_user_id (user_id)
);

-- Security logs table
CREATE TABLE IF NOT EXISTS security_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    event_type VARCHAR(50) NOT NULL,
    severity ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
    message TEXT NOT NULL,
    details JSON NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    user_id INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_event_type (event_type),
    INDEX idx_severity (severity),
    INDEX idx_created_at (created_at),
    INDEX idx_ip_address (ip_address)
);

-- Settings table
CREATE TABLE IF NOT EXISTS settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT NULL,
    setting_type ENUM('string', 'integer', 'boolean', 'json') DEFAULT 'string',
    description TEXT NULL,
    is_public BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_setting_key (setting_key),
    INDEX idx_is_public (is_public)
);

-- Insert default settings
INSERT INTO settings (setting_key, setting_value, setting_type, description, is_public) VALUES
('app_name', 'Text Search & Replace Tool', 'string', 'Application name', TRUE),
('app_version', '2.0.0', 'string', 'Application version', TRUE),
('max_text_length', '100000', 'integer', 'Maximum text length allowed', TRUE),
('cache_enabled', 'true', 'boolean', 'Enable caching system', FALSE),
('cache_duration', '300', 'integer', 'Cache duration in seconds', FALSE),
('rate_limit_requests', '100', 'integer', 'Rate limit requests per hour', FALSE),
('debug_mode', 'false', 'boolean', 'Enable debug mode', FALSE),
('maintenance_mode', 'false', 'boolean', 'Enable maintenance mode', FALSE)
ON DUPLICATE KEY UPDATE updated_at = CURRENT_TIMESTAMP;

-- Create indexes for better performance
CREATE INDEX idx_search_history_composite ON search_history(user_id, created_at);
CREATE INDEX idx_saved_searches_composite ON saved_searches(user_id, name);
CREATE INDEX idx_analytics_composite ON analytics(event_type, created_at);

-- Create views for common queries
CREATE OR REPLACE VIEW popular_searches AS
SELECT 
    search_term,
    COUNT(*) as search_count,
    AVG(occurrences_found) as avg_occurrences,
    MAX(created_at) as last_searched
FROM search_history 
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY search_term 
HAVING search_count > 1
ORDER BY search_count DESC, last_searched DESC
LIMIT 100;

CREATE OR REPLACE VIEW user_statistics AS
SELECT 
    u.id,
    u.username,
    u.email,
    COUNT(sh.id) as total_searches,
    COUNT(ss.id) as saved_searches_count,
    COUNT(tt.id) as templates_count,
    u.created_at as user_since,
    u.last_login
FROM users u
LEFT JOIN search_history sh ON u.id = sh.user_id
LEFT JOIN saved_searches ss ON u.id = ss.user_id
LEFT JOIN text_templates tt ON u.id = tt.user_id
GROUP BY u.id, u.username, u.email, u.created_at, u.last_login;

-- Stored procedures for common operations
DELIMITER //

CREATE PROCEDURE GetUserSearchHistory(IN user_id INT, IN limit_count INT)
BEGIN
    SELECT 
        search_term,
        replace_term,
        occurrences_found,
        case_sensitive,
        use_regex,
        created_at
    FROM search_history 
    WHERE user_id = user_id 
    ORDER BY created_at DESC 
    LIMIT limit_count;
END //

CREATE PROCEDURE CleanOldData(IN days_to_keep INT)
BEGIN
    -- Clean old search history
    DELETE FROM search_history 
    WHERE created_at < DATE_SUB(NOW(), INTERVAL days_to_keep DAY);
    
    -- Clean old analytics
    DELETE FROM analytics 
    WHERE created_at < DATE_SUB(NOW(), INTERVAL days_to_keep DAY);
    
    -- Clean old security logs (keep critical ones longer)
    DELETE FROM security_logs 
    WHERE created_at < DATE_SUB(NOW(), INTERVAL days_to_keep DAY)
    AND severity NOT IN ('high', 'critical');
    
    SELECT ROW_COUNT() as rows_deleted;
END //

DELIMITER ;

-- Triggers for automatic cleanup and logging
DELIMITER //

CREATE TRIGGER after_user_login
AFTER UPDATE ON users
FOR EACH ROW
BEGIN
    IF NEW.last_login != OLD.last_login THEN
        INSERT INTO analytics (event_type, event_data, user_id, created_at)
        VALUES ('user_login', JSON_OBJECT('previous_login', OLD.last_login), NEW.id, NOW());
    END IF;
END //

CREATE TRIGGER after_search_insert
AFTER INSERT ON search_history
FOR EACH ROW
BEGIN
    INSERT INTO analytics (event_type, event_data, user_id, ip_address, created_at)
    VALUES (
        'search_performed', 
        JSON_OBJECT(
            'search_length', LENGTH(NEW.search_term),
            'text_length', NEW.text_length,
            'occurrences', NEW.occurrences_found,
            'use_regex', NEW.use_regex
        ),
        NEW.user_id,
        NEW.ip_address,
        NOW()
    );
END //

DELIMITER ;
